/**
 * Tabulator Component - a Vue.js component that wraps the Tabulator library.
 *
 * @fires backButtonCallback - Emits event when the back button is clicked.
 * @fires primaryButtonCallback - Emits event when the primary button is clicked.
 * @fires localRefresh - Emits event when data should be refreshed from a local source.
 * @fires handleSecondaryButtonClick - Emits event when one of the secondary buttons is clicked.
 *
 * @slot custom-header - A slot for adding custom header content.
 * @slot back-button - A slot for adding a custom back button.
 * @slot primary-button - A slot for adding a custom primary button.
 *
 * @prop {Object} [tabulatorConfiguration={}] - Configuration object for the Tabulator table.
 * @prop {String} [title=""] - Title to display in the card header.
 * @prop {Boolean} [showBackButton=false] - Flag to show/hide the back button.
 * @prop {Boolean} [showPrimaryButton=false] - Flag to show/hide the primary button.
 * @prop {Array} [secondaryButtonsConfig=[]] - Configuration array for the secondary buttons.
 * @prop {Object} [primaryButtonConfig={}] - Configuration object for the primary button.
 * @prop {Boolean} [showClearAllFiltersButton=false] - Flag to show/hide the clear all filters button.
 * @prop {Boolean} [showRefreshButton=false] - Flag to show/hide the refresh button.
 * @prop {Boolean} [showColumnConfigButton=false] - Flag to show/hide the column cofig button.
 * @prop {Boolean} [showDownloadExcelButton=false] - Flag to show/hide the download excel button.
 * @prop {Boolean} [showGoogleSheetsButton=false] - Flag to show/hide the Google Sheets button.
 * @prop {Boolean} [showDownloadPdfButton=false] - Flag to show/hide the download PDF button.
 * @prop {Boolean} [showDownloadCsvButton=false] - Flag to show/hide the download CSV button.
 * @prop {String} [nameOfFileExport=""] - Name of the exported file.
 * @prop {String} [refreshType=""] - Type of the refresh operation (local or server).
 * @prop {String} [refreshTypeServerUrl=""] - Server URL for refresh operation when refreshType is 'server'.
 * @prop {Function} [refreshTypeLocalCallback] - Callback function for refresh operation when refreshType is 'local'.
 * @prop {String} [secondaryDivHtml=""] - HTML content for the secondary div.
 * @prop {Boolean} [inDialog = true] - This will ensure that the resize logic is not applied if it it in a dialog.
 * @prop {Boolean} [hasSibling = false] - This will ensure that the resize logic is not applied if it has a sibling.
 * @prop {Boolean} [showHeader = true] - This will hide the default header and display tabulator rows only.
 * Extensions to tabulator column defination:
 * @prop {String} [headerFilter = 'daterange'] - This will show the daterange filter for the header column.
 * @prop {Boolean} [columnMenu = true] - This will show the hide/show column filter(headerMenu) for the header column.
 *
 * @method refreshData - Refreshes data in the tabulator. The type of refresh (local or server) depends on the 'refreshType' prop.
 * @method backButtonMethod - Method that triggers the backButtonCallback event.
 * @method primaryButtonMethod - Method that triggers the primaryButtonCallback event.
 * @method clearAllHeaderFilters - Clears all filters applied to headers in the tabulator.
 * @method getData - Returns data from the tabulator.
 * @method downloadPdf - Initiates a download of the tabulator data as a PDF file.
 * @method downloadXls - Initiates a download of the tabulator data as an Excel file.
 * @method getAllData - Returns all data from the tabulator.
 * @method handleSecondaryButtonClick - Method that triggers an event corresponding to a secondary button being clicked.
 * @method initTabulator - Initializes the tabulator with the configuration provided.
 *
 * @component
 */


const DEFAULT_TABULATOR_CONFIG = {
  locale: true,
  placeholder: "No Data Available",
  placeholderHeaderFilter: "No Matching Data",
  langs: {
    ...Object.fromEntries(['en-us', 'en'].map(key => [key, {
      pagination: {
        page_size: "Rows",
        first: "<<",
        first_title: "First Page",
        last: ">>",
        last_title: "Last Page",
        prev: "<",
        prev_title: "Prev Page",
        next: ">",
        next_title: "Next Page",
        counter: {
          showing: "",
          of: "of",
          rows: "rows",
          pages: "pages"
        }
      }
    }])),
  }
};
//Importing tabulator JS
import "../../imports/tabulator.min.js";
import "../../imports/tabulatorhelpers/jspdf.umd.min.js";
import "../../imports/tabulatorhelpers/jspdf.plugin.autotable.min.js";

//NOTE: Can put these formatters in seperate file and import here.
Tabulator.extendModule("format", "formatters", {
  linkFormatter: function (cell, formatterParams, onRendered) {
    var value = cell.getValue();
    if (!value) {
      return '';
    }
    return (
      "<span style='color:#1867c0; font-weight:bold;'>" +
      value +
      "</span>"
    );
  },
  boldFontFormatter: function (cell, formatterParams, onRendered) {
    var value = cell.getValue();
    cell.getElement().style.fontWeight = 500;
    return value;
  }
});

Tabulator.extendModule("edit", "editors", {
  dateEditor: function (cell, onRendered, success, cancel, editorParams) {
    const allowedSelectionTypes = ['date', 'datetime'];
    if (!editorParams || !editorParams.selectionType || !allowedSelectionTypes.includes(editorParams.selectionType)) {
      console.warn("Invalid selectionType provided for dateEditor. Defaulting to 'datetime'.");
      editorParams.selectionType = 'datetime';
    }
    const calendarInstance = new Vue({
      vuetify: new Vuetify(),
      data() {
        return {
          movedPopovers: [],
          breakCycle: false,
          selectionTypeInputField: editorParams.selectionType === 'datetime' ? 'datetime-local' : 'date',
          selectionType: editorParams.selectionType,
          inputValue: null,
          selectedDate: cell.getValue() ? new Date(cell.getValue() * 1000) : null
        };
      },
      mounted() {
        this.inputValue = this.selectedDate ? this.formatDate(this.selectedDate) : null;
        //In Vue3 we can move this with transport.
        const popovers = this.$el.querySelectorAll('.vc-popover-content-wrapper');
        const vApp = document.querySelector('.v-application');
        popovers.forEach((element) => {
          element.originalParent = element.parentElement;
          vApp.appendChild(element);
          element.style.zIndex = '500';
          this.movedPopovers.push(element);
        });
      },
      beforeDestroy() {        
        this.movedPopovers.forEach((element) => {
          if (element.originalParent && document.body.contains(element.originalParent)) {
            element.originalParent.appendChild(element);
            element.style.zIndex = '';
          } else {
            element.remove();
          }
        });
        this.movedPopovers = [];
      },  
      methods: {
        blurTextInput(e) {
          //Check if target of this blur event is not the date picker.          
          if (!e.relatedTarget || !e.relatedTarget.closest('.vc-popover-content-wrapper')) {
            this.cancelDialog();
          } else {            
            e.preventDefault();
            e.stopPropagation();
            //Keep the focus on the text field. use timeout to gain focus after the blur event.
            setTimeout(() => {
                this.$refs.datePickerTextField?.focus();
            }, 0);
          }
        },
        // Format date properly for input field
        formatDate(date) {
          if (!date) return null;
          // Get local date components
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // Ensure 2 digits
          const day = String(date.getDate()).padStart(2, '0');

          // Format YYYY-MM-DD
          let formattedDate = `${year}-${month}-${day}`;

          if (this.selectionTypeInputField === 'datetime-local') {
              // Get local time components
              const hours = String(date.getHours()).padStart(2, '0');
              const minutes = String(date.getMinutes()).padStart(2, '0');

              return `${formattedDate}T${hours}:${minutes}`;
          }

          return formattedDate;
        },
        handleInput() {
          this.breakCycle = true;
          if (!this.$refs.datePickerTextField.$refs.input.valueAsDate) {
            this.selectedDate = null;
            return
          }
          let utcDate = this.$refs.datePickerTextField.$refs.input.valueAsDate;
          const tzoffset = utcDate.getTimezoneOffset() * 60000; //offset in milliseconds
          this.selectedDate = new Date(utcDate.getTime() + tzoffset);
          this.$refs.datePicker.move(this.selectedDate)
        },
        updateDate(value) {
          if (this.breakCycle) {this.breakCycle = false; return};
          this.inputValue = this.formatDate(value);
        },
        applyDate() {
          // Apply and close when the Done button is clicked
          success(this.selectedDate ? (this.selectedDate.getTime() / 1000) : null);
          this.closePopup();
        },
        submit(e){
          e.preventDefault();
          e.stopPropagation();
          this.applyDate();
        },
        cancelEdit(e) {
          e.preventDefault();
          e.stopPropagation();
          this.cancelDialog();
        },
        cancelDialog() {
          // Close the dialog without saving changes when the Cancel button is clicked
          cancel();
          this.closePopup();
        },
        closePopup() {
          this.$destroy(); // Destroy Vue instance
          this.$el.remove(); // Remove the element from the DOM
        },
        clickEvent(e) {
          e.preventDefault();
          e.stopPropagation();
        },
      },
      template: /*HTML*/`
        <div>
          <v-date-picker ref="datePicker" :popover="{ positionFixed: true, visibility: 'visible', keepVisibleOnInput: true }"  v-model="selectedDate" :show-current="true" style="border-radius: 10px 10px 0px 0px !important;" @input="updateDate" :mode="selectionType">
            <template v-slot="{ inputEvents }">
              <v-text-field @keydown.enter="submit" @keydown.esc="cancelEdit" @click="clickEvent" v-model="inputValue" @input="handleInput" :type="selectionTypeInputField" ref="datePickerTextField" @blur="blurTextInput" class="custom-text-field-date-picker-remove-border" style="height: 100%; margin-top: 0px !important"  hide-details dense>
                <template v-slot:prepend-inner><v-icon class="pr-1">mdi-calendar</v-icon></template>
              </v-text-field>
            </template>
            <template #footer>
            <div style="display: flex; justify-content: space-between; width: 50%;">
              <v-btn style="border-radius: 0px !important;" color="gray" block @click="cancelDialog">Cancel</v-btn>
              <v-btn style="border-radius: 0px !important;" color="primary" block @click="applyDate">Done</v-btn>
            </div>
            </template>
          </v-date-picker>
        </div>
      `
    });
    onRendered(function(){
      calendarInstance.$refs.datePickerTextField.focus();
      setTimeout(() => {
        calendarInstance.$refs.datePicker.showPopover();
      }, 100);
    });
    calendarInstance.$mount();
    return calendarInstance.$el;
  },
  daterange: function (cell, onRendered, success, cancel, editorParams) {
    const isHeaderFilter = cell.getType() === "header";
    const value = cell.getValue();
    const calendarInstance = new Vue({
      vuetify: new Vuetify(),
      template: /*HTML*/`
      <v-date-picker ref="datePicker" is-range mode="dateTime" v-model="range" :model-config="modelConfig" :popover="{ positionFixed: true }" >
        <template v-slot="{ inputValue, inputEvents, showPopover }">
          <v-text-field style="max-width: 100% !important;" clearable @click:clear="clearValue" @keydown.prevent @keydown.enter="submit" @keydown.esc="cancelEdit" @blur="blurTextInput" hide-details ref="datePickerTextField" prepend-inner-icon="mdi-calendar" @focus="showPopover" :value="humanizedRange" class="custom-text-field-date-picker-text-color custom-text-field-icon-color custom-text-field-date-picker pa-0 ma-0"/>
        </template>
        <template #footer>
          <div style="display: flex; justify-content: space-between; width: 50%;">
            <v-btn class="tabulator-edit-list-cancel" style="border-radius: 0px !important;" color="gray" block @click="cancelDialog">Cancel</v-btn>
            <v-btn class="tabulator-edit-list-done" style="border-radius: 0px !important; color: #1976d2 !important;" color="gray" block @click="applyDate">Done</v-btn>
          </div>
        </template>
      </v-date-picker>`,
      mounted() {
        //In Vue3 we can move this with transport.
        const popovers = this.$el.querySelectorAll('.vc-popover-content-wrapper');
        const vApp = document.querySelector('.v-application');
        popovers.forEach((element) => {
          element.originalParent = element.parentElement;
          vApp.appendChild(element);
          element.style.zIndex = '500';
          this.movedPopovers.push(element);
        });
      },
      beforeDestroy() {
        this.movedPopovers.forEach((element) => {
          if (element.originalParent && document.body.contains(element.originalParent)) {
            element.originalParent.appendChild(element);
            element.style.zIndex = '';
          } else {
            element.remove();
          }
        });
        this.movedPopovers = [];
      },
    
      data: function () {
        return {
          isHeaderFilter: isHeaderFilter,
          movedPopovers: [],
          modelConfig: {
            start: {
              type: 'string',
              mask: 'iso'
            },
            end: {
              type: 'string',
              mask: 'iso'
            },
          },
          previousRange: {
            start: value?.start,
            end: value?.end,
          },
          range: {
            start: value?.start,
            end: value?.end,
          },
        };
      },
      methods: {
        clearValue() {
          this.range = { start: null, end: null };
          this.previousRange = { start: null, end: null };
          this.applyDate();
        },
        blurTextInput(e) {          
          //Check if target of this blur event is not the date picker.          
          if (!e.relatedTarget || !e.relatedTarget.closest('.vc-popover-content-wrapper')) {
            this.cancelDialog();
          } else {            
            e.preventDefault();
            e.stopPropagation();
            //Keep the focus on the text field. use timeout to gain focus after the blur event.
            setTimeout(() => {
                this.$refs.datePickerTextField?.focus();
            }, 0);
          }
        },
        applyDate() {
          // Apply and close when the Done button is clicked
          this.previousRange = this.range;
          success({ start: this.range.start, end: this.range.end });
          this.closePopup();
        },
        submit(e){
          e.preventDefault();
          e.stopPropagation();
          this.applyDate();
        },
        cancelEdit(e) {
          e.preventDefault();
          e.stopPropagation();
          this.cancelDialog();
        },
        cancelDialog() {
          // Close the dialog without saving changes when the Cancel button is clicked
          cancel();
          this.range = this.previousRange;
          this.closePopup();
        },
        closePopup() {
          if (this.isHeaderFilter) {
            this.$refs.datePickerTextField?.blur();
            this.$refs.datePicker.hidePopover();
          } else {
            this.$destroy(); // Destroy Vue instance
            this.$el.remove(); // Remove the element from the DOM
          }
        },
        clickEvent(e) {
          e.preventDefault();
          e.stopPropagation();
        },
      },
      computed: {
        humanizedRange() {          
          let DateTime = luxon.DateTime;
          if (!this.range || (!this.range.start && !this.range.end)) return null;
    
          let startLuxon = DateTime.fromISO(this.range.start);
          let endLuxon = DateTime.fromISO(this.range.end);      
          if (startLuxon.hasSame(endLuxon, 'month')) {
            return startLuxon.toFormat('d') + "-" + endLuxon.toFormat('d MMM');
          } else {
            return startLuxon.toFormat('d MMM') + " - " + endLuxon.toFormat('d MMM');
          }
    
        }
      }
    });
    onRendered(function(){
      if (!isHeaderFilter) {
        calendarInstance.$refs.datePickerTextField.focus();
        setTimeout(() => {
          calendarInstance.$refs.datePicker.showPopover();
        }, 100);
      }
    });
    calendarInstance.$mount();
    return calendarInstance.$el;
  },
});

export default {
  template: /*html*/
    `
  <v-card flat id="tabulator-component-vcard">
    <v-card-title ref="tabulator-header" v-if="showHeader" style="padding-top:12px;padding-bottom:12px">
      <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'" :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
        {{ snackbar.text }}
        <v-btn color="pink" text @click="snackbar.snackbar = false"> Close </v-btn>
      </v-snackbar>
      <template v-if="showBackButton">
        <v-icon style="color: black" @click="backButtonMethod">mdi-arrow-left</v-icon>
      </template>
      <template v-else>
        <slot name="back-button"></slot>
      </template>
      <span>
      {{this.title}}
      </span>

      <template v-if="showPrimaryButton">
        <v-btn v-if="showPrimaryButton" @click="primaryButtonMethod" rounded color="primary" depressed class="text-center" style="background-color:white;text-transform:none; margin-left:30px;">
          <v-icon  left dark>
            {{primaryButtonConfig.icon}}
          </v-icon>
          {{primaryButtonConfig.title}}

        </v-btn>
      </template>
      <template v-else>
        <slot name="primary-button"></slot>
      </template>

      <!-- If secondaryButtonsConfig is empty, use the named slot -->
      <template v-if="secondaryButtonsConfig.length === 0">
        <slot name="custom-header"></slot>
      </template>
      <!-- If secondaryButtonsConfig is not empty, render buttons -->
      <template v-else>
        <v-btn v-for="(button, index) in secondaryButtonsConfig" :key="index" v-if="selectedRowsJson.length > 0" rounded :color="button.icon.color" depressed class="text-center" @click="handleSecondaryButtonClick(button)" :loading="button.loading" style="background-color:white;text-transform:none; margin-left:30px;">
          <v-icon v-if="button.icon.showIcon"  left dark>
            {{button.icon.icon}}
          </v-icon>
          <v-flex>
          {{button.title}}
         </v-flex>
          
        </v-btn>
      </template>
      <v-spacer v-if="hasLeftSideButtons" ></v-spacer>
      
      <!-- Desktop view -->
      <div class="d-none d-sm-flex">
        <slot name="custom-icon-buttons"></slot>
        <v-btn v-if="showClearAllFiltersButton" icon @click="clearAllHeaderFilters" color="black" title="Clear Filters">
          <v-icon>mdi-filter-off</v-icon>
        </v-btn>
        <v-btn :loading="helperButtonsLoader" v-if="showRefreshButton" icon @click="refreshData" color="black"  title="Refresh Table">
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
        <v-menu offset-y :close-on-content-click="false" v-if="showColumnConfigButtonComputed">
          <template v-slot:activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on" color="black" title="Manage Columns">
              <v-icon>mdi-view-column-outline</v-icon>
            </v-btn>
          </template>
          <v-card>
            <v-card-text style="max-height: 450px; overflow-y: auto">
              <draggable :list="tabulatorLoadedColumns" :move="canDragColumn" @end="onDragEnd" item-key="field">
                <v-row v-for="(column, index) in tabulatorLoadedColumns" align="center" justify="space-between" :key="index" dense :class="{ 'draggable-item': !column.frozen }" v-show="column.field">
                  <v-col cols="auto" class="px-0">
                    <v-simple-checkbox :key="index" color="primary" v-model="column.visible" @input="tabulator.getColumn(column.field).toggle();"/>
                  </v-col>
                  <v-col>
                    <label :class="{ 'draggable-item': !column.frozen }" style="font-weight: 500 !important; font-size: 0.8rem !important; color: rgba(0, 0, 0, 0.87);">{{ column.title }}</label>
                  </v-col>
                  <v-col class="px-0" cols="auto" v-if="!(column.frozen ?? false)">
                    <v-icon color="grey">mdi-drag-vertical</v-icon>
                  </v-col>
                </v-row>
                </draggable>
            </v-card-text>
          </v-card>
        </v-menu>
        <v-btn v-if="showDownloadCsvButton" icon @click="downloadCSV" color="primary" title="Export to CSV">
          <v-icon>mdi-file-delimited-outline</v-icon>
        </v-btn>
        <v-btn v-if="showDownloadExcelButton" icon @click="downloadXls" color="primary" title="Export to Excel">
          <v-icon>mdi-table-arrow-down</v-icon>
        </v-btn>
        <v-btn v-if="showGoogleSheetsButton" :loading="isCreatingGoogleSheet" icon @click="createGoogleSheet" color="#0f9d58" title="Export to Google Sheets">
          <v-icon>mdi-google-spreadsheet</v-icon>
        </v-btn>
        <!-- <v-btn v-if="showDownloadPdfButton" icon @click="downloadPdf" color="black" title="Export to PDF">
          <v-icon>mdi-file-pdf-box</v-icon>
        </v-btn> -->
        <slot name="custom-icon-buttons-end"></slot>
      </div>

      <!-- Mobile view -->
      <div class="d-sm-none" v-if="hasLeftSideButtons">
        <v-menu offset-y>
          <template v-slot:activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on">
              <v-icon>mdi-dots-vertical</v-icon>
            </v-btn>
          </template>
          
          <v-list>
            <template v-if="$slots['custom-icon-buttons']">
              <slot name="custom-icon-buttons"></slot>
            </template>
            
            <v-list-item v-if="showClearAllFiltersButton" @click="clearAllHeaderFilters">
              <v-list-item-icon>
                <v-icon>mdi-filter-off</v-icon>
              </v-list-item-icon>
              <v-list-item-title>Clear Filters</v-list-item-title>
            </v-list-item>

            <v-list-item v-if="showRefreshButton" @click="refreshData">
              <v-list-item-icon>
                <v-icon>mdi-refresh</v-icon>
              </v-list-item-icon>
              <v-list-item-title>Refresh Table</v-list-item-title>
            </v-list-item>
            <v-menu offset-y :close-on-content-click="false" v-if="showColumnConfigButtonComputed">
              <template v-slot:activator="{ on, attrs }">
              <v-list-item v-bind="attrs" v-on="on">
                <v-list-item-icon>
                  <v-icon color="black">mdi-view-column-outline</v-icon>
                </v-list-item-icon>
                <v-list-item-title>Manage Columns</v-list-item-title>
              </v-list-item>
              </template>
              <v-card>
                <v-card-text style="max-height: 450px; overflow-y: auto">
                  <draggable :list="tabulatorLoadedColumns" :move="canDragColumn" @end="onDragEnd" item-key="field">
                    <v-row v-for="(column, index) in tabulatorLoadedColumns" align="center" justify="space-between" :key="index" dense :class="{ 'draggable-item': !column.frozen }" v-show="column.field">
                      <v-col cols="auto" class="px-0">
                        <v-simple-checkbox :key="index" color="primary" v-model="column.visible" @input="tabulator.getColumn(column.field).toggle();"/>
                      </v-col>
                      <v-col>
                        <label :class="{ 'draggable-item': !column.frozen }" style="font-weight: 500 !important; font-size: 0.8rem !important; color: rgba(0, 0, 0, 0.87);">{{ column.title }}</label>
                      </v-col>
                      <v-col class="px-0" cols="auto" v-if="!(column.frozen ?? false)">
                        <v-icon color="grey">mdi-drag-vertical</v-icon>
                      </v-col>
                    </v-row>
                    </draggable>
                </v-card-text>
              </v-card>
            </v-menu>
            <v-list-item v-if="showDownloadCsvButton" @click="downloadCSV">
              <v-list-item-icon>
                <v-icon>mdi-file-delimited-outline</v-icon>
              </v-list-item-icon>
              <v-list-item-title>Export to CSV</v-list-item-title>
            </v-list-item>

            <v-list-item v-if="showDownloadExcelButton" @click="downloadXls">
              <v-list-item-icon>
                <v-icon>mdi-table-arrow-down</v-icon>
              </v-list-item-icon>
              <v-list-item-title>Export to Excel</v-list-item-title>
            </v-list-item>

            <v-list-item v-if="showGoogleSheetsButton" @click="createGoogleSheet" :disabled="isCreatingGoogleSheet">
              <v-list-item-icon>
                <v-icon color="#0f9d58">mdi-google-spreadsheet</v-icon>
              </v-list-item-icon>
              <v-list-item-title>Export to Google Sheets</v-list-item-title>
            </v-list-item>

            <!--<v-list-item v-if="showDownloadPdfButton" @click="downloadPdf">
              <v-list-item-icon>
                <v-icon>mdi-file-pdf-box</v-icon>
              </v-list-item-icon>
              <v-list-item-title>Export to PDF</v-list-item-title>
            </v-list-item> -->

            <template v-if="$slots['custom-icon-buttons-end']">
              <slot name="custom-icon-buttons-end"></slot>
            </template>
          </v-list>
        </v-menu>
      </div>
    </v-card-title>
    <div id="tabulator-custom-tabs">
      <slot name="tabs"></slot>
    </div>
    <v-card-text class="py-0">
      <div :id="uniqueId" >
        <div :ref="uniqueRef"></div>
      </div>
    </v-card-text>
  </v-card>
    `,
  components: {},
  props: {
    tabulatorConfiguration: {
      type: Object,
      required: false,
      default: () => ({}), // default is an empty object
    },
    title: {
      type: String,
      default: "",
    },
    showBackButton: {
      type: Boolean,
      default: false,
    },
    showPrimaryButton: {
      type: Boolean,
      default: false,
    },
    secondaryButtonsConfig: {
      type: Array,
      default: () => [],
    },
    primaryButtonConfig: {
      type: Object,
      default: () => ({}),
    },
    showClearAllFiltersButton: {
      type: Boolean,
      default: false,
    },
    showRefreshButton: {
      type: Boolean,
      default: false,
    },
    showColumnConfigButton: {
      type: Boolean,
      default: false,
    },
    showDownloadExcelButton: {
      type: Boolean,
      default: false,
    },
    showGoogleSheetsButton: {
      type: Boolean,
      default: false,
    },
    showDownloadPdfButton: {
      type: Boolean,
      default: false,
    },
    showDownloadCsvButton: {
      type: Boolean,
      default: false,
    },
    nameOfFileExport: {
      type: String,
      default: "",
    },
    refreshType: {
      type: String,
      default: "",
    },
    refreshTypeServerUrl: {
      type: String,
      default: "",
    },
    refreshTypeLocalCallback: {
      type: Function,
      default: () => { },
    },
    secondaryDivHtml: {
      type: String,
      default: "",
    },
    inDialog: {
      type: Boolean,
      default: false,
    },
    hasSibling: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    clearSelectionsOnDownloads: {
      type: Boolean,
      default: false
    }
  },

  data: function () {
    return {
      helperButtonsLoader: false,
      isCreatingGoogleSheet: false,
      tabulator: null,
      id: 1,
      headerFilterInstances: [],
      observer: null,
      uniqueRef: 'tabulatortable' + this._uid,
      uniqueId: 'tabulatorContainer' + this._uid,
      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'left',
        mode: '',
        timeout: 2000,
        text: ''
      },
      tabulatorLoadedColumns: [],
      showColumnConfigButtonViaColumn: false,
      showMobileMenu: false,
      isDestroyed: false
    }
  },
  watch: {
    /* tabulatorConfiguration(newVal) {
       if (newVal) {
         this.initTabulator();
       }
     } */
  },

  updated() {

  },
  methods: {
    canDragColumn(evt) {
      const isFrozen = evt.draggedContext.element.frozen ?? false;
      //Check if destination is frozen?
      const isTargetFrozen = evt.relatedContext.element.frozen ?? false;
      return !isFrozen && !isTargetFrozen;
    },
    onDragEnd(event) {      
      const { oldIndex, newIndex } = event;
      if (oldIndex !== newIndex) {
        const draggedColumn = this.tabulator.getColumn(this.tabulatorLoadedColumns[newIndex].field);
        //Find the column before this index or after based on new index.
        if (newIndex > 0) {
          const targetColumn = this.tabulatorLoadedColumns[newIndex - 1];
          if (targetColumn.field) {
            draggedColumn.move(targetColumn.field, true); // Move target to newIndex
            return;
          }
        } 
        if (newIndex < this.tabulatorLoadedColumns.length - 1) {
          const targetColumn = this.tabulatorLoadedColumns[newIndex + 1];
          draggedColumn.move(targetColumn.field, false); // Move target to newIndex
        }
      }
    },
    createButton(iconClass, tooltip, onClick, color = "black", show = true) {
      let button = document.createElement("button");
      if (!show) {
        button.style.display = "none";
      }
      button.type = "button";
      button.title = tooltip;
      button.className = "v-btn v-btn--flat v-btn--icon v-btn--round mr-2 theme--light v-size--x-small";

      let span = document.createElement("span");
      span.className = "v-btn__content";

      let icon = document.createElement("i");
      icon.className = "v-icon notranslate mdi " + iconClass + " theme--light";
      icon.style.fontSize = "16px";
      icon.style.color = color;

      span.appendChild(icon);
      button.appendChild(span);

      button.addEventListener("click", onClick);
      return button;
    },
    showAlert(message) {
      this.snackbar.text = message;
      this.snackbar.snackbar = true;
    },

    checkForSelectAllPrompt(selectedData, selectedRows) {
      if (!this.tabulator) return;

      const activeRows = this.tabulator.getRows("active");
      const totalRowsOnPage = activeRows.length;
      const selectedCount = selectedData.length;

      // Check if all rows on current page are selected
      if (selectedCount === totalRowsOnPage && totalRowsOnPage > 0) {
        // For server-side pagination, get total from the last AJAX response
        let totalCount = totalRowsOnPage;

        if (this.tabulator.modules && this.tabulator.modules.ajax) {
          // Try to get total from the last server response
          const ajaxResponse = this.tabulator.modules.ajax.response;
          if (ajaxResponse) {
            // Different endpoints use different field names for total count
            totalCount = ajaxResponse.total || ajaxResponse.count || ajaxResponse.last_row || totalRowsOnPage;
          }
        }

        // Fallback: try to get from pagination module
        if (totalCount === totalRowsOnPage && this.tabulator.modules.page) {
          const pageInfo = this.tabulator.modules.page;
          if (pageInfo.remotePaging && pageInfo.max) {
            totalCount = pageInfo.max;
          }
        }

        // Additional fallback: check if parent component has totalMatchingCount
        if (totalCount === totalRowsOnPage && this.$parent && this.$parent.totalMatchingCount) {
          totalCount = this.$parent.totalMatchingCount;
        }

        // Final fallback: try to get from tabulator's pagination info
        if (totalCount === totalRowsOnPage && this.tabulator.getPageMax) {
          const pageMax = this.tabulator.getPageMax();
          const pageSize = this.tabulator.getPageSize();
          if (pageMax && pageSize) {
            totalCount = pageMax * pageSize;
          }
        }

        // Only emit if there are more items than what's on current page
        if (totalCount > totalRowsOnPage) {
          this.$emit("selectAllMatchingPrompt", {
            pageCount: totalRowsOnPage,
            totalCount: totalCount
          });
        }
      }
    },
    debounce(func, wait, immediate) {
      let timeout;
      return function executedFunction() {
        const context = this;
        const args = arguments;

        const later = function () {
          timeout = null;
          if (!immediate) func.apply(context, args);
        };

        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);

        if (callNow) func.apply(context, args);
      };
    },
    onResize() {

      this.$nextTick(() => {
        if (this.inDialog) {
          const currentElement = document.getElementById(this.uniqueId);
          if (this.hasSibling) {
            let parent = currentElement.parentNode;
            let counter = 10;
            while (!parent.style.height || counter < 0) {
              counter--;
              parent = parent.parentNode
            }
            const header = this.$refs['tabulator-header'];
            //TODO: Need to add logic to handle the case when tabulator-custom-tabs is present.
            if (this.showHeader && header) {
              //Get tabulator-header ref height.  
              currentElement.style.height = `calc(${parent.style.height} - ${header.clientHeight}px)`;
            } else {
              currentElement.style.height = parent.style.height;
            }
            return
          }
          const containerElement = currentElement.closest('.v-dialog');

          if (containerElement) {

            const clone = containerElement.cloneNode(true);

            const nodeToExclude = clone.querySelector(`#${this.uniqueId}`);
            if (nodeToExclude) {

              nodeToExclude.parentNode.removeChild(nodeToExclude);
            }

            this.setHeightToAuto(clone, this.uniqueId);
            clone.style.visibility = 'hidden';
            clone.style.position = 'absolute';
            document.body.appendChild(clone);

            // Calculate the height
            const height = clone.offsetHeight;
            document.body.removeChild(clone);


            const uniqueIdHeight = containerElement.clientHeight - height;
            currentElement.style.height = `${uniqueIdHeight}px`;
          }
        } else {

          const height = window.innerHeight;
          const element = document.getElementById(this.uniqueId);

          if (element) {
            const top = element.getBoundingClientRect().top;
            element.style.height = `${height - top - 1}px`;
          }
        }
      });
    },

    setHeightToAuto(element, id) {

      element.style.height = element.id === id ? '0px' : 'auto';
      if (element.id == id) {
        return;
      }
      Array.from(element.children).forEach(child => {
        this.setHeightToAuto(child, id);
      });
    },
    handleSecondaryButtonClick(button) {
      if (this.$listeners[button.callback]) {
        this.$emit(button.callback, button.id);
      }
    },
    async refreshData() {
      if (this.tabulator) {
        if (this.refreshType == 'server') {
          this.helperButtonsLoader = true;
          await this.tabulator.setData(this.tabulatorConfiguration.ajaxURL, { cacheBust: true });
          this.helperButtonsLoader = false;
        } else if (this.refreshType == 'local') {
          this.$emit("localRefresh")
          //Check for refreshTypeLocalCallback is function or not. if yes call it.
          if (typeof this.refreshTypeLocalCallback == "function") {
            this.refreshTypeLocalCallback();
          }
        }
      }
    },
    handleDateRangeFilter(headerValue, rowValue, rowData, filterParams) {
      //headerValue - the value of the header filter element
      //rowValue - the value of the column in this row
      //rowData - the data for the row being filtered
      //filterParams - params object passed to the headerFilterFuncParams property

      //convert strings into dates

      if (!!headerValue.start) {
        headerValue.start = new Date(headerValue.start);
      }
      if (!!headerValue.end) {
        headerValue.end = new Date(headerValue.end);
      }

      //compare dates
      if (rowValue) {
        rowValue = rowValue.toString().length == 10 ? new Date(rowValue * 1000) : new Date(rowValue);

        if (!!headerValue.start) {
          if (!!headerValue.end) {
            return moment(rowValue) >= headerValue.start && rowValue <= headerValue.end;
          } else {
            return rowValue >= headerValue.start;
          }
        } else {
          if (!!headerValue.end) {
            return rowValue <= headerValue.end;
          }
        }
      }

      return true;
    },

    backButtonMethod() {
      this.$emit('backButtonCallback')
    },
    primaryButtonMethod() {
      this.$emit('primaryButtonCallback')
    },
    clearAllHeaderFilters() {
      this.tabulator.clearHeaderFilter();
      //Clear selections
      if (this.clearSelectionsOnDownloads) {
        this.tabulator.deselectRow();
      }
    },
    getData() {
      let data = this.tabulator.getData();
      return data;
    },
    downloadPdf() {
      if (this.tabulator) {
        if (this.tabulatorConfiguration.downloadRowRange == 'selected' && this.tabulator.getSelectedRows().length == 0) {
          this.showAlert('Please select rows to export.');
          return
        }
        this.tabulator.download("pdf", `${this.nameOfFileExport}.pdf`, {
          orientation: "landscape",
          title: `${this.nameOfFileExport}`
        });
        //Clear selections
        if (this.clearSelectionsOnDownloads) {
          this.tabulator.deselectRow();
        }
      }
    },
    downloadCSV() {
      if (this.tabulator) {
        if (this.tabulatorConfiguration.downloadRowRange == 'selected' && this.tabulator.getSelectedRows().length == 0) {
          this.showAlert('Please select rows to export.');
          return
        }
        this.tabulator.download("csv", `${this.nameOfFileExport}.csv`);
        //Clear selections
        if (this.clearSelectionsOnDownloads) {
          this.tabulator.deselectRow();
        }
      }
    },


    downloadXls() {
      if (this.tabulator) {
        if (this.tabulatorConfiguration.downloadRowRange == 'selected' && this.tabulator.getSelectedRows().length == 0) {
          this.showAlert('Please select rows to export.');
          return;
        }
        this.tabulator.download("xlsx", `${this.nameOfFileExport}.xlsx`, {
          documentProcessing: function (workbook) {
            // 1) Utility to remove control chars & flatten newlines
            function sanitizeCellContent(str) {
              if (!str) return '';
              str = String(str); // Convert to string if not already
              str = str.replace(/[^\x09\x0A\x0D\x20-\x7E]/g, "");
              str = str.replace(/\r?\n/g, " ");
              return str.trim();
            }
          
            // 2) Fix common "https:/" mistakes → "https://"
            function fixCommonSlash(url) {
              if (!url || typeof url !== 'string') return '';
              return url.replace(/^(\s*)(https?:)\/([^/])/, "$1$2//$3");
            }
          
            // 3) Check if a single URL is valid (after slash-fix)
            function getValidUrl(url) {
              if (!url || typeof url !== "string") return null;
              let trimmed = url.trim();
              if (!/^https?:\/\/\S+$/i.test(trimmed)) return null;
              try {
                const encoded = encodeURI(trimmed);
                if (!/^https?:\/\/\S+$/i.test(encoded)) return null;
                return encoded;
              } catch (err) {
                return null;
              }
            }
          
            // 4) Parse cell value. If it's a JSON array, return that array.
            function getCellItems(value) {
              if (!value) return [];
              try {
                const parsed = JSON.parse(value);
                return Array.isArray(parsed) ? parsed : [String(value)];
              } catch {
                return String(value).split(",");
              }
            }

            // Process each sheet
            workbook.SheetNames.forEach(sheetName => {
              const sheet = workbook.Sheets[sheetName];
              if (!sheet || !sheet["!ref"]) return;

              const range = XLSX.utils.decode_range(sheet["!ref"]);
          
              const minColumnWidth = 40;
              let colWidths = [];
              let columnsToRemove = [];
          
              // (A) Identify empty columns
              for (let col = range.s.c; col <= range.e.c; col++) {
                let isEmpty = true;
                for (let row = range.s.r; row <= range.e.r; row++) {
                  const cellRef = XLSX.utils.encode_cell({ c: col, r: row });
                  const cell = sheet[cellRef];
                  if (cell && cell.v) {
                    isEmpty = false;
                    break;
                  }
                }
                if (isEmpty) {
                  columnsToRemove.push(col);
                } else {
                  colWidths.push({ wch: minColumnWidth });
                }
              }
          
              // Remove empty columns
              columnsToRemove.reverse().forEach(col => {
                for (let row = range.s.r; row <= range.e.r; row++) {
                  const cellRef = XLSX.utils.encode_cell({ c: col, r: row });
                  delete sheet[cellRef];
                }
              });
          
              // Shift columns to close the gap
              columnsToRemove.forEach(colToRemove => {
                for (let row = range.s.r; row <= range.e.r; row++) {
                  for (let col = colToRemove + 1; col <= range.e.c; col++) {
                    const fromRef = XLSX.utils.encode_cell({ c: col, r: row });
                    const toRef = XLSX.utils.encode_cell({ c: col - 1, r: row });
                    sheet[toRef] = sheet[fromRef];
                    delete sheet[fromRef];
                  }
                }
              });
          
              // Filter colWidths to ignore removed columns
              colWidths = colWidths.filter((_, idx) => !columnsToRemove.includes(idx));
              sheet["!cols"] = colWidths;
          
              // Update "!ref" if needed
              if (columnsToRemove.length > 0) {
                const newRange = {
                  s: { r: range.s.r, c: range.s.c },
                  e: { r: range.e.r, c: range.e.c - columnsToRemove.length },
                };
                sheet["!ref"] = XLSX.utils.encode_range(newRange);
              }
          
              // (B) Optional: color the header row
              for (let col = range.s.c; col <= range.e.c - columnsToRemove.length; col++) {
                const headerCellRef = XLSX.utils.encode_cell({ c: col, r: range.s.r });
                const headerCell = sheet[headerCellRef];
                if (headerCell) {
                  headerCell.s = {
                    fill: { fgColor: { rgb: "ADD8E6" } } // light blue
                  };
                }
              }
          
              // (C) Process each cell → decide hyperlink vs. text
              for (let row = range.s.r; row <= range.e.r; row++) {
                for (let col = range.s.c; col <= range.e.c - columnsToRemove.length; col++) {
                  const cellRef = XLSX.utils.encode_cell({ c: col, r: row });
                  const cell = sheet[cellRef];
                  if (!cell || !cell.v) continue;
          
                  // Step 1: sanitize the text
                  let sanitizedVal = sanitizeCellContent(cell.v.toString());
                  // Remove any existing hyperlink
                  delete cell.l;
          
                  // Step 2: parse items (JSON array or comma-split)
                  let items = getCellItems(sanitizedVal);
          
                  // Step 3: fix "https:/" slash issues, then test each
                  let validUrls = items
                    .map(fixCommonSlash)
                    .map(getValidUrl)
                    .filter(u => u !== null);
          
                  if (validUrls.length === 1 && items.length === 1) {
                    // Exactly one item total, exactly one valid link
                    cell.v = validUrls[0]; // store the single link
                    cell.l = { Target: validUrls[0] }; // single hyperlink
                    cell.s = {
                      font: { color: { rgb: "0000FF" }, underline: true }
                    };
                  } else if (validUrls.length === items.length && items.length > 1) {
                    // The user has multiple URLs, all valid
                    // We store them as plain text separated by " | "
                    cell.v = items.map(i => sanitizeCellContent(i)).join(" | ");
                    delete cell.s; // no styling
                  } else {
                    // Otherwise, 0 valid or partial valid → just store as text
                    // (some might be invalid, or there's multiple items but only 1 valid link)
                    // to avoid partial hyperlink
                    cell.v = cell.v.toString()
                    delete cell.s;
                  }
                }
              }
            });
          
            // Return updated workbook
            return workbook;
          }
        });

        if (this.clearSelectionsOnDownloads) {
          this.tabulator.deselectRow();
        }
      }
    },

    async createGoogleSheet() {
      if (this.tabulatorConfiguration.downloadRowRange == 'selected' && this.tabulator.getSelectedRows().length == 0) {
        this.showAlert('Please select rows to export.');
        return
      }
      this.isCreatingGoogleSheet = true;
      const list = this.tabulator.modules.export.generateExportList({}, false, this.tabulatorConfiguration.downloadRowRange, 'download');
      var fileContents = [];
      //TODO: If required add header titles instead of field titles.
      list.forEach((row) => {
        var item = {};
        switch (row.type) {
          case "header":
            break;
          case "group":
            console.warn("Download Warning - JSON downloader cannot process row groups");
            break;
          case "calc":
            console.warn("Download Warning - JSON downloader cannot process column calculations");
            break;
          case "row":
            row.columns.forEach((col) => {
              if (col) {
                item[col.component.getTitleDownload() || col.component.getField()] = col.value;
              }
            });
            fileContents.push(item);
            break;
        }
      });
      let exportToGoogle = await fetch('/node/reports/export-report-to-google-sheet', {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          data: fileContents,
          sheet_name: this.nameOfFileExport
        })
      });
      if (exportToGoogle.ok) {
        exportToGoogle = await exportToGoogle.text();
        window.open(exportToGoogle, '_blank');
      }
      //Clear selections
      if (this.clearSelectionsOnDownloads) {
        this.tabulator.deselectRow();
      }
      this.isCreatingGoogleSheet = false;
    },
    getAllData() {
      return this.tabulator.getData();
    },
    isEmptyObject(obj) {
      return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
    },
    async initTabulator() {
      await new Promise((resolve, reject) => {
        const loop = () => {
          if (this.isDestroyed) {
            reject('Component destroyed before Tabulator could be initialized.');
            return;
          }
          if (
            this.tabulatorConfiguration === undefined ||
            this.tabulatorConfiguration === null ||
            this.isEmptyObject(this.tabulatorConfiguration) ||
            this.$refs[this.uniqueRef] === undefined
          ) {
            setTimeout(loop, 100);
          } else {
            resolve(true);
          }
        };
        loop();
      })
      .catch(err => {
        return;
      });

      const config = Object.assign({}, DEFAULT_TABULATOR_CONFIG, this.tabulatorConfiguration);
      if (config.persistence && config.persistence.columns === true) {
        config.persistence.columns = ["visible", "frozen"];
      }
      if (this.tabulator) {
        this.destroyTabulator();
      }
      let self = this;
      const mapColumns = function (config, columns) {
        return columns.map(column => {
          if (column.headerFilter === 'daterange') {
            column.headerFilterFunc = self.handleDateRangeFilter;
            column.headerFilterEmptyCheck = (value) => {
              return !value || !value.start || !value.end;
            }
          }
          const multiselect = column.headerFilterParams != undefined
            ? typeof column.headerFilterParams == 'function'
              ? column.headerFilterParams().multiselect == true
              : column.headerFilterParams.multiselect == true
            : false;
          const valuesLookup = column.headerFilterParams != undefined
            ? typeof column.headerFilterParams == 'function'
              ? column.headerFilterParams().valuesLookup == true
              : column.headerFilterParams.valuesLookup == true
            : false;
          if (column.headerFilter === 'list' && multiselect && (config.filterMode === 'local' || valuesLookup) && column.headerFilterFunc == undefined) {
            column.headerFilterFunc = (headerValue, rowValue, rowData) => {
              return Array.isArray(headerValue) ? headerValue.some(item => item === rowValue) : false;
            };
          }
          if (column.headerFilter === 'list' && multiselect && (config.filterMode === 'local' || valuesLookup) && column.headerFilterEmptyCheck == undefined) {
            column.headerFilterEmptyCheck = (value) => {
              return value.length == 0;
            }
          }
          if (column.columnMenu === true) {
            delete column.columnMenu;
            self.showColumnConfigButtonViaColumn = true;
          }
          return column;
        });
      }
      if (config.columns) {
        config.columns = mapColumns(config, config.columns);
      } else if (config.autoColumnsDefinitions && typeof config.autoColumnsDefinitions != 'function') {
        config.autoColumnsDefinitions = mapColumns(config, config.autoColumnsDefinitions);
      } else if (config.autoColumnsDefinitions && typeof config.autoColumnsDefinitions == 'function') {
        //TODO: Need to figure out how to apply above values.
      }
      if(this.$refs[this.uniqueRef] === undefined) {
        // Temporary fix till we figure out the detailed reasons.
        // On sites details page, a few of the tabulators are getting recycled quickly
        console.error(`Tabulator ref ${this.uniqueRef}  not found!`);
        return;
      }
      const tempTabulator = new Tabulator(this.$refs[this.uniqueRef], config);
      
      // Wait until Tabulator is built. (With proper cleanup)
      await new Promise(resolve => tempTabulator.on('tableBuilt', async () => {
        this.$emit('tableBuilt');
        resolve();
      }));

      this.tabulator = tempTabulator;

      // Fix for Tabulator bug #4079
      let originalInitialize = this.tabulator.modules.filter.initialize;
      this.tabulator.modules.filter.initialize = function () {
        this.subscribe("column-delete", col => {
          this.headerFilterColumns = this.headerFilterColumns.filter(x => x !== col);
        });
        originalInitialize.call(self.tabulator.modules.filter);
      };

      for (const eventName in this.$listeners) {
        this.tabulator.on(eventName, (...args) => {
          this.$emit(eventName, ...args);
        });
      }

      // Enhanced row selection handling for Gmail-style select all
      this.tabulator.on("rowSelectionChanged", (data, rows) => {
        this.$emit("rowSelectionChanged", data, rows);
        this.checkForSelectAllPrompt(data, rows);
      });
      this.tabulator.on("columnsLoaded", (columns) => {
        this.reloadColumns();
      });
      this.tabulator.on("columnMoved", (column, columns) => {
        const columnObjectIndex = this.tabulatorLoadedColumns.findIndex(c => c.field === column.getField());
        const columnIndex = this.tabulator.getColumns().findIndex(c => c.getField() === column.getField());
        if (columnIndex !== columnObjectIndex) {
          this.reloadColumns();
        }
      });
      this.tabulator.on("columnVisibilityChanged", (column, visible) => {
        const columnObject = this.tabulatorLoadedColumns.find(c => c.field === column.getField());
        if (!columnObject || column.isVisible() !== columnObject.visible) {
          this.reloadColumns();
        }
      });
      // Unsubscribe from Tabulator event bus when columns are added or removed.
      this.tabulator.eventBus.subscribe("column-delete", (col) => {
        this.$nextTick(() => {
          this.reloadColumns();
        });
      });
      this.tabulator.eventBus.subscribe("column-add", (col) => {
        this.$nextTick(() => {
          this.reloadColumns();
        });
      });
      this.reloadColumns();
      this.$emit('tabulatorInitialized');
    },
    reloadColumns() {
      this.tabulatorLoadedColumns = this.tabulator.getColumns().map(column => {
        const columnDefinition = column.getDefinition();
        return {visible: column.isVisible(), field: columnDefinition.field, frozen: Object.hasOwn(columnDefinition, 'frozen') ? columnDefinition.frozen : false, title: columnDefinition.title};
      });
    },
    destroyTabulator() {
      for (const eventName in this.$listeners) {
        this.tabulator.off(eventName);
      };
      this.tabulator.off("columnsLoaded");
      this.tabulator.off("columnMoved");
      this.tabulator.off("columnVisibilityChanged");
      this.tabulator.off("tableBuilt");
      this.tabulator.eventBus.unsubscribe("column-delete");
      this.tabulator.eventBus.unsubscribe("column-add");
      this.headerFilterInstances.forEach(instance => {
        instance.$destroy();
        if (instance.$el && instance.$el.parentNode) {
          instance.$el.parentNode.removeChild(instance.$el);
        }
      });
      this.headerFilterInstances = [];
      this.tabulator.destroy();
      this.tabulator = null;
    }
  },
  beforeDestroy() {
    // Set isDestroyed flag so that if the polling loop is still in progress it will stop.
    this.isDestroyed = true;

    if (this.tabulator) {
      this.destroyTabulator();
    }
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    window.removeEventListener('resize', this.onResize);
  },
  destroyed() {
    if (this.tabulator) {
      try {
        this.tabulator.destroy();
      } catch (e) {
        console.warn('Error destroying tabulator:', e);
      }
      this.tabulator = null;
    }
    
    if (this.headerFilterInstances && this.headerFilterInstances.length > 0) {
      this.headerFilterInstances.forEach(instance => {
        try {
          if (instance && typeof instance.$destroy === 'function') {
            instance.$destroy();
          }
          if (instance && instance.$el && instance.$el.parentNode) {
            instance.$el.parentNode.removeChild(instance.$el);
          }
        } catch (e) {
          console.warn('Error cleaning up header filter instance:', e);
        }
      });
      this.headerFilterInstances = [];
    }
  },
  computed: {
    hasLeftSideButtons() {
      return this.showClearAllFiltersButton || this.showRefreshButton || this.showDownloadExcelButton || this.showGoogleSheetsButton || this.showDownloadCsvButton || this.showColumnConfigButtonComputed || (!!this.$slots['custom-icon-buttons-end']);
    },
    showColumnConfigButtonComputed() {
      return (this.showColumnConfigButton || this.showColumnConfigButtonViaColumn) && this.tabulatorLoadedColumns.length > 0;
    },
    selectedRowsJson() {
      return this.tabulator ? this.tabulator.getSelectedData() : [];
    }
  },

  mounted() {
    this.initTabulator();
    window.addEventListener('resize', this.onResize);
    Vue.nextTick(() => {
      this.onResize();
    });

    if (typeof this.$el !== 'undefined' && this.$el instanceof Element) {
      this.observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          this.onResize();
        }
      });
      this.observer.observe(this.$el);
    }
  }
}
