import tabulatorDatatable from "../../components/tabulator/tabulator-datatable.js";
import FormSubmit from "../form-submit.js";
import pricingEditor from "../../components/pricing-editor.js";
import postdateddashboard from "../../components/postdated/postdateddashboard.js";
import { EventBus } from "../../eventbus.js";
import rangeDatePicker from "../../components/sitefotos/range-date-picker.js";

let formIdToColorMap = {};
let colors = ["#1867c0", "#dabc12"];
let currentColor = colors[0];

//define column header menu as column visibility toggle
export default {
  template: /*html*/`
    <div id="post-dated-services-manager">
    <v-app-bar
          app
          fixed
          elevate-on-scroll
          clipped-left
          class="mainback"
          style="border-bottom:1px solid #e5e5e5;">
        <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
        <div class="">
          <img src="/images/sitefotos_logo_icon.svg" style="width:44px; height:44px;padding-right:10px;">
        </div>
        <span class="page-titles">{{$route.meta.ptitle}}</span>
        <v-spacer></v-spacer>

      <range-date-picker label="Start End Date" style="width: 300px" v-model="range" :default-range="range"></range-date-picker>
      
      <v-spacer></v-spacer>

        <div class="nav-block">
          <v-img :src="cLogo" max-height="36" contain max-width="72" :alt="cName"
                 style="display: inline-block"></v-img>
          <v-menu offset-y bottom style="max-width: 200px">
            <template v-slot:activator="{ on, attrs }">
              <v-avatar color="purple" size="36" class="ml-2" v-bind="attrs" v-on="on">
                <span class="white--text headline">{{initials}}</span>
              </v-avatar>
            </template>
            <v-list>
              <v-list-item to="/account">
                <v-list-item-title>Settings</v-list-item-title>
              </v-list-item>
              <v-list-item @click="logout()">
                <v-list-item-title>Log Off</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
        <v-menu offset-y :close-on-content-click='false'>
          <template v-slot:activator="{ on }">
            <v-app-bar-nav-icon v-on="on" class="hidden-md-and-up">
              <v-icon>more_vert</v-icon>
            </v-app-bar-nav-icon>
          </template>
          <v-card>
            <v-list dense>
            </v-list>
          </v-card>
        </v-menu>
      </v-app-bar>

    <v-progress-linear indeterminate color="primary" v-if="getDataLoader"></v-progress-linear>

    <!-- Gmail-style Select All Prompt -->
    <v-alert v-if="showSelectAllPrompt" type="info" dense class="ma-2">
      <div class="d-flex align-center">
        <span class="flex-grow-1">{{selectAllPromptMessage}}</span>
        <v-btn text small color="primary" @click="confirmSelectAllMatching" class="ml-2">
          Select all matching
        </v-btn>
        <v-btn text small @click="cancelSelectAllMatching" class="ml-1">
          Cancel
        </v-btn>
      </div>
    </v-alert>

    <!-- Removed green "all matching selected" bar - unnecessary visual clutter that goes out of sync -->

    <tabulator-datatable
      v-if="tabulatorReady && !loading && tabulatorConfiguration != undefined"
      ref="pricingDashboard"
      :tabulator-configuration="tabulatorConfiguration"
      title="Service History"
      :primary-button-config='{
        "icon": "mdi-plus",
        "title": "New Services"
      }'
      :show-primary-button="pagePermissionAdmin||pagePermissionSuperAdmin"
      :show-clear-all-filters-button="true"
      :show-download-excel-button="true"
      :show-refresh-button="true"
      :name-of-file-export="exportFileName"
      refresh-type="remote"
      @primaryButtonCallback="primaryButtonCallback"
      @rowSelectionChanged="processSelectionCount"
      @selectAllMatchingPrompt="onSelectAllMatchingPrompt"
      @remoteRefresh="fetchData"
      @dataLoaded="onDataLoaded"
      @pageLoaded="onPageLoaded"
      @ajaxResponse="onAjaxResponse"
      :clear-selections-on-downloads="true"
    >
    <template v-slot:custom-header>
          <v-spacer></v-spacer>
      <v-btn :disabled="!tableData || tableData.length <= 0" color="black" icon @click="collapseTable">
        <v-icon v-if="!tableCollapsed">mdi-arrow-collapse</v-icon>
        <v-icon v-else>mdi-arrow-expand</v-icon>
      </v-btn>
          <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="sendEmailDialog=true;" color="black" :disabled="emailButtonDisabled" title="Send email to contractor">
          <v-icon>mdi-email</v-icon>
        </v-btn>
        <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin"  icon @click="snowApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Apply snow total">
        <v-icon>mdi-snowflake</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="peopleApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Change People">
        <v-icon>mdi-account-multiple</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="hoursApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Change Hours">
        <v-icon>mdi-clock-time-nine-outline</v-icon>
      </v-btn>
      <v-btn v-show="false"  icon @click="" color="black" :disabled="emailButtonDisabled" title="Update Status">
        <v-icon>mdi-grease-pencil</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" icon @click="eventApplyDialog=true" color="black" :disabled="emailButtonDisabled" title="Assign to event">
      <v-icon>mdi-weather-snowy</v-icon>
    </v-btn>

      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" :loading="rejectServicesLoading" icon @click="rejectServices" color="black" :disabled="emailButtonDisabled" :title="getActionTooltip('Reject')">
        <v-icon>mdi-cancel</v-icon>
        <span v-if="getSelectedCount() > 0" class="ml-1 caption">({{getSelectedCount()}})</span>
      </v-btn>

      <v-btn v-if="pagePermissionAdmin || pagePermissionSuperAdmin" :loading="approveServicesLoading" icon @click="approveServices('APPROVED')" color="black" :disabled="emailButtonDisabled" :title="getActionTooltip('Approve')">
        <v-icon>mdi-check-all</v-icon>
        <span v-if="getSelectedCount() > 0" class="ml-1 caption">({{getSelectedCount()}})</span>
      </v-btn>


      <v-btn v-if="pagePermissionSuperAdmin" :loading="approveServicesLoading" icon @click="approveServices('LOCKED')" color="black" :disabled="emailButtonDisabled" :title="getActionTooltip('Lock')">
        <v-icon>mdi-lock</v-icon>
        <span v-if="getSelectedCount() > 0" class="ml-1 caption">({{getSelectedCount()}})</span>
      </v-btn>

      <v-btn v-if="pagePermissionAccounting" icon color="black" :disabled="invoiceButtonDisabled" :loading="invoiceLoading" title="Create Invoice" @click="createInvoice">
        <v-icon color="rgba(0,0,54,.54)">sitefotos-quickbooks</v-icon>
      </v-btn>
      <v-btn v-if="pagePermissionAccounting" icon color="black"  :loading="billLoading"  :disabled="emailButtonDisabled" title="Create Bill" @click="createBill">
        <v-icon color="rgba(0,54,0,.54)">sitefotos-quickbooks</v-icon>
      </v-btn>
      <v-btn v-if=" vendorAccessCode == 'f182c4c2e7b4bd91debd2d0d636becac' && pagePermissionSuperAdmin " color="black" icon title="Postdated Emails" @click="openPostdatedDashboard">
        <v-icon>mdi-email-check</v-icon>
      </v-btn>
    </template>
    </tabulator-datatable>
    <v-dialog persistent v-model="snowApplyDialog" max-width="600px">
    <v-card>
      <v-toolbar class="elevation-0 white">
        <v-toolbar-title>
          <h3 class="headline mb-0">Apply Snow Total</h3>
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-toolbar-items></v-toolbar-items>
        <v-btn icon @click="snowApplyDialog=false">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-text-field type="number" label="Snow Total" v-model="snowTotal" ></v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="snowApplyDialog=false">Cancel</v-btn>
        <v-btn color="primary" :loading="snowTotalApplyLoading" @click="applySnowTotalBulk()">Apply</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog persistent v-model="eventApplyDialog" max-width="600px">
    <v-card>
      <v-toolbar class="elevation-0 white">
        <v-toolbar-title>
          <h3 class="headline mb-0">Apply Event</h3>
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-toolbar-items></v-toolbar-items>
        <v-btn icon @click="eventApplyDialog=false">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-text-field outlined hide-details dense type="text" label="Event Name" v-model="eventName" ></v-text-field>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="eventApplyDialog=false">Cancel</v-btn>
        <v-btn color="primary" :loading="eventApplyLoading" @click="applyEventBulk()">Apply</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

    <v-dialog v-model="peopleApplyDialog" persistent max-width="600px">
      <v-card>
        <v-toolbar class="elevation-0 white">
          <v-toolbar-title>
            <h3 class="headline mb-0">Apply People</h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items></v-toolbar-items>
          <v-btn icon @click="peopleApplyDialog=false">
            <v-icon>close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-text-field type="number" outlined hide-details dense label="People" v-model="peopleTotal" ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="peopleApplyDialog=false">Cancel</v-btn>
          <v-btn color="primary" :loading="peopleTotalApplyLoading" @click="applyPeopleToBulk()">Apply</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="hoursApplyDialog" persistent max-width="600px">
      <v-card>
        <v-toolbar class="elevation-0 white">
          <v-toolbar-title>
            <h3 class="headline mb-0">Apply Hours</h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items></v-toolbar-items>
          <v-btn icon @click="hoursApplyDialog=false">
            <v-icon>close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-text-field type="number" outlined hide-details dense label="Hours" v-model="hoursTotal" ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="hoursApplyDialog=false">Cancel</v-btn>
          <v-btn color="primary" :loading="hoursTotalApplyLoading" @click="applyHoursToBulk()">Apply</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog persistent v-model="sendEmailDialog" fullscreen>
      <div style="display: flex; align-items: center; justify-content: center; background-color: #00000050; height: 100%">
        <div style="height: 600px; width: 600px;">
          <v-card>
            <v-toolbar class="elevation-0 white">
              <v-toolbar-title>
                <h3 class="headline mb-0">Services Approval Email</h3>
              </v-toolbar-title>
              <v-spacer></v-spacer>
              <v-toolbar-items></v-toolbar-items>
              <v-btn icon @click="sendEmailDialog=false; resetDate();">
                <v-icon>close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text>
              <v-col class="mt-5 pa-0">
                <span style="color: black;">Hi [CONTRACTOR],</span>
                <v-textarea
                    style="position: relative"
                    class="mt-5"
                    rows="4"
                    v-model="emailBody"
                    label="Email Text"
                    outlined hide-details dense
                >
                </v-textarea>
                <div class="mt-3" style="display: flex; flex-direction: row; align-items: center; height: 20px;justify-content: end;">
                  <span>
                      <v-checkbox v-model="chBoxSaveEmailBodyAsTemplate" hide-details>
                        <template v-slot:label>
                          <div class='mb-4'>
                            Save Template
                          </div>
                        </template>
                      </v-checkbox>
                    </span>
                </div>
                <v-date-picker
                    is-range
                    mode="dateTime"
                    v-model="dateRangePicker.range"
                    :model-config="dateRangePicker.modelConfig">
                  <template v-slot="{ inputValue, inputEvents, togglePopover }">
                    <div class="flex items-center" style="font-size: 12px;">
                      <v-btn style="text-align: left; height: 36px; width: 100%;" text class="pa-0 v-label input-date theme--light mt-5" @click="togglePopover()">
                        <span style="margin-left: 6px; text-transform: initial; font-size: 0.8rem; font-weight: 500; letter-spacing: normal; border-bottom: 1px solid rgb(0 0 0 / 42%); padding-bottom: 10px; display: flex; justify-content: space-between; width: 100%;">
                          <span style="margin-top: 11px; " v-if="dateRangePicker.range == null">Checkin Checkout Range</span>
                          <span style="margin-top: 11px; color: rgba(0, 0, 0, 0.87); font-weight: 500" v-else>{{humanizedRange}}</span>
                          <v-icon class='mt-2'>arrow_drop_down</v-icon>
                        </span>
                      </v-btn>
                    </div>
                  </template>
                </v-date-picker>
                <br><span class="mt-3" style="color: black;">Any changes or additions are subject to admin approval by {{cName}}</span>
                <br><span style="text-decoration: underline; color: blue; cursor: pointer;">View Service History</span>
              </v-col>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-checkbox v-model="chBoxIncludePricingColumn"  class="mr-5">
                <template v-slot:label>
                  <div class='mb-4'>
                    Include Pricing Column
                  </div>
                </template>
              </v-checkbox>
              <v-btn color="primary" :loading="emailButtonLoading" @click="sendReportInEmail" depressed tile>Send</v-btn>
            </v-card-actions>
          </v-card>
        </div>
      </div>
    </v-dialog>

    <v-dialog scrollable v-model="formSubmitDialog" persistent max-width="500px">
      <form-submit :selectedSiteID="siteIDForFormSubmit" source="ADMIN" device-model="SERVICEHISTORY" @closeFormSubmit="closeFormSubmit($event)"></form-submit>
    </v-dialog>
    
    <v-dialog :key="pricingProps.buildingId" v-model="pricingProps.show" max-width="95vw" :width=" !overallFetch ? '10vw':'95vw' " persistent scrollable>
      <pricing-editor
          v-if="pricingProps.loaded"
          v-bind:pricing-type="pricingProps.pricingType"
          :contract-type=pricingProps.contractType
          :copy-mode="pricingProps.copyMode"
          :parent-id="pricingProps.parentId"
          :trade-id="pricingProps.tradeId"
          :pricingType="pricingProps.pricingType"
          :buildingId="pricingProps.buildingId"
          :slaData="[]"
          :calculatedPricingFromDb="pricingProps.pricingJson"
          :selectedEventRange="[]"
          :customForecasts="[]"
          :serviceLevels="[]"
          :eventRanges="[]"
          :materialPricing="[]"
          :servicePricing="[]"
          :disable-role-selector="true"
          @onCancel="onCancel"
          @onComponentFetchDone="onComponentFetchDone">
      </pricing-editor>
      <v-row v-else style="background-color: white;" justify="center">
        <v-col align="center">
          <v-progress-circular indeterminate></v-progress-circular>
        </v-col>
      </v-row>
    </v-dialog>
    <template>
  <v-dialog v-model="billDialog" max-width="500px">
    <v-card>
      <v-card-title>
        Creating Bills
      </v-card-title>
      <v-card-text style="overflow:auto">
        <v-progress-linear :value="billProgress" buffer-value="0" stream></v-progress-linear>
        <v-list height="300px">
          <v-list-item-group v-if="billResults.length > 0">
            <v-list-item v-for="(result, index) in billResults" :key="index">
              <v-list-item-content>
                <v-list-item-title>{{ result.contractorName }}</v-list-item-title>
                <v-list-item-subtitle style="white-space:normal; overflow: visible" :class="{ 'error--text': result.status === 'error', 'success--text': result.status === 'success' }">
                  {{ result.message }}
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list-item-group>
          <v-alert v-else type="info">
            Processing...
          </v-alert>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
  <v-dialog v-model="invoiceDialog" max-width="500px">
    <v-card>
      <v-card-title>
        Creating Invoices
      </v-card-title>
      <v-card-text style="overflow:auto">
        <v-progress-linear :value="invoiceProgress" buffer-value="0" stream></v-progress-linear>
        <v-list height="300px">
          <v-list-item-group v-if="invoiceResults.length > 0">
            <v-list-item v-for="(result, index) in invoiceResults" :key="index">
              <v-list-item-content>
                <v-list-item-title>{{ result.clientName }}</v-list-item-title>
                <v-list-item-subtitle style="white-space:normal;overflow:visible" :class="{ 'error--text': result.status === 'error', 'success--text': result.status === 'success' }">
                  {{ result.message }}
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list-item-group>
          <v-alert v-else type="info">
            Processing...
          </v-alert>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
      
      <v-dialog v-model="info.vDialog" width="500">
        <v-card>
          <v-card-title>{{info.actionPerformed}}</v-card-title>
          <v-card-text>
            <p>{{info.successRows}}</p>
            <p>{{info.failedRows}}</p>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" tile @click="info.vDialog=false">Done</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      
      <v-dialog persistent v-model="dialogPostdatedDashboard" fullscreen :hide-overlay="$vuetify.breakpoint.smAndDown" width="85vw" transition="dialog-bottom-transition">
        <postdateddashboard @dismissPostdated="dialogPostdatedDashboard=false"/>
      </v-dialog>

      <!-- Bulk Action Progress Dialog -->
      <v-dialog v-model="bulkActionProgress.show" persistent max-width="600px">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="mr-2" color="primary">mdi-cog</v-icon>
            {{bulkActionProgress.action}}
          </v-card-title>
          <v-card-text>
            <v-progress-linear
              :value="bulkActionProgress.percentage"
              height="25"
              color="primary"
              striped
              :indeterminate="bulkActionProgress.percentage < 5">
              <template v-slot:default="{ value }">
                <strong class="white--text">{{ Math.ceil(value) }}%</strong>
              </template>
            </v-progress-linear>

            <div class="mt-3 text-center">
              <div class="subtitle-1">
                Processing {{bulkActionProgress.total}} items
              </div>
              <div v-if="bulkActionProgress.estimatedTimeRemaining !== null" class="caption text--secondary">
                <span v-if="bulkActionProgress.estimatedTimeRemaining > 0">
                  Estimated time remaining: {{bulkActionProgress.estimatedTimeRemaining}} seconds
                </span>
                <span v-else-if="bulkActionProgress.percentage >= 100">
                  Complete!
                </span>
              </div>
            </div>

            <div class="mt-4" v-if="bulkActionProgress.percentage >= 100">
              <v-divider class="mb-3"></v-divider>
              <div class="text-center">
                <div v-if="bulkActionProgress.results.success.length" class="success--text mb-1">
                  <v-icon color="success" class="mr-1">mdi-check-circle</v-icon>
                  {{bulkActionProgress.results.success.length}} items processed successfully
                </div>
                <div v-if="bulkActionProgress.results.failed.length" class="error--text">
                  <v-icon color="error" class="mr-1">mdi-alert-circle</v-icon>
                  {{bulkActionProgress.results.failed.length}} items failed
                </div>
              </div>
            </div>

            <!-- Warning for large operations -->
            <v-alert v-if="bulkActionProgress.total > 1000" type="warning" dense class="mt-3">
              <v-icon slot="prepend">mdi-information</v-icon>
              Large operation in progress. Please do not close this window.
            </v-alert>
          </v-card-text>
          <v-card-actions v-if="bulkActionProgress.percentage >= 100">
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="bulkActionProgress.show = false" depressed>
              <v-icon left>mdi-check</v-icon>
              Done
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
</template>

    <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
    :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">
    {{ snackbar.text }}
    <v-btn color="pink" text @click="snackbar.snackbar = false">
      Close
    </v-btn>
  </v-snackbar>
    </div>
  `,
  components: {
    tabulatorDatatable,
    FormSubmit,
    pricingEditor,
    postdateddashboard,
    rangeDatePicker
  },

  created: function () {

  },

  beforeDestroy() {
    // Clean up event listeners
    EventBus.$off('onCancel', this.onCancel);
    EventBus.$off('onComponentFetchDone', this.onComponentFetchDone);
  },

  data: function () {
    return {
      tabulatorReady: false, // Added for controlled initialization
      range: {
        start: null,
        end: null,
      },
      dialogPostdatedDashboard: false,
      dateRangePicker: {
        modelConfig: {
          start: {
            type: 'string',
            mask: 'iso',
          },
          end: {
            type: 'string',
            mask: 'iso',
          },
        },
        range: null,
      },
      info: {
        vDialog: false,
        actionPerformed: null,
        successRows: null,
        failedRows: null
      },
      pageID: 24,
      billDialog: false,
      billResults: [],
      billProgress: 0,
      billLoading: false,
      pricingProps: {
        tradeId: 0,
        parentId: 0,
        buildingId: 0,
        show: false,
        loaded: true,
        pricingType: '',
        contractType: 'Snow Removal',
        pricingJson: {},
        componentFetchDone: false,
        copyMode: false
      },
      invoiceDialog: false,
      invoiceResults: [],
      invoiceProgress: 0,
      invoiceLoading: false,

      snackbar: {
        snackbar: false,
        y: 'bottom',
        x: 'right',
        mode: '',
        timeout: 6000,
        text: ''
      },
      chBoxIncludePricingColumn: true,
      chBoxSaveEmailBodyAsTemplate: true,
      emailBody: 'Please click the link below to view and edit and/or approve all services performed by you and your crews on the sites. Any changes or additions are subject to admin approval.',
      sendEmailDialog: false,
      tableCollapsed: false,
      //FORMSUBMIT VARIABLES
      formSubmitDialog: false,
      siteIDForFormSubmit: false,
      clients: [],

      peopleTotalApplyLoading: false,
      peopleApplyDialog: false,
      peopleTotal: null,

      hoursTotalApplyLoading: false,
      hoursApplyDialog: false,
      hoursTotal: null,

      snowTotalApplyLoading: false,
      snowApplyDialog: false,
      snowTotal: null,

      eventApplyLoading: false,
      eventApplyDialog: false,
      eventName: null,


      rejectServicesLoading: false,
      getDataLoader: false,
      sDate: null,
      eDate: null,
      emailButtonDisabled: true,
      invoiceButtonDisabled: true,
      approveServicesButtonDisabled: true,
      emailButtonLoading: false,
      approveServicesLoading: false,
      loading: false,
      tabulatorConfiguration: {},
      tableData: [

      ],
      originalData: [], // This will no longer hold the full dataset for server-side paging
      filterValues: {}, // To store dynamic filter values
      currentPageUncollapsedData: [],

      // Gmail-style select all state management
      selectionMode: 'none', // 'none', 'page', 'all-matching'
      totalMatchingCount: 0,  // Total items matching current filters
      selectedPageItems: [], // Currently selected items on page
      allMatchingFilters: null, // Snapshot of filters when "all matching" selected
      showSelectAllPrompt: false,
      selectAllPromptMessage: '',
      isRestoringSelections: false, // Flag to prevent interference during selection restoration
      deselectedIds: new Set(), // Track deselected items when in all-matching mode
      isFilterChange: false, // Flag to track if data fetch is due to filter/date change
      lastFilterState: null, // Track last filter state to detect changes

      // Persistent selection tracking across pages
      persistentSelectedIds: new Set(), // Track selected row IDs across all pages
      bulkActionProgress: {
        show: false,
        action: '',
        percentage: 0,
        processed: 0,
        total: 0,
        results: { success: [], failed: [] }
      }
      }
    },
    watch: {
      range: {
          handler: 'fetchData', // Re-fetch data when date range changes
          deep: true
      },
      snowApplyDialog(val) {

      this.snowTotal = '';

    }
  },

  methods: {
    // Gmail-style select all methods
    onSelectAllMatchingPrompt(data) {
      // Don't show the prompt if we're already in all-matching mode
      if (this.selectionMode === 'all-matching') {
        return;
      }

      this.showSelectAllPrompt = true;
      this.selectAllPromptMessage = `All ${data.pageCount} items on this page are selected. Select all ${data.totalCount} items that match your current filters?`;
      this.totalMatchingCount = data.totalCount;
    },

    onDataLoaded() {
      // Called when tabulator finishes loading remote data
      if (this.isFilterChange) {
        // Data loaded due to filter/date change - don't restore selections
        console.log('🔄 Data loaded due to filter change - not restoring selections');
        this.isFilterChange = false;
      } else {
        // Data loaded due to pagination - restore selections
        console.log('📄 Data loaded due to pagination - restoring selections');
        this.restoreSelections();
      }
    },

    onPageLoaded() {
      this.restoreSelections();
    },

    onAjaxResponse() {
      // Event handler for AJAX response
    },

    confirmSelectAllMatching() {
      this.selectionMode = 'all-matching';
      this.allMatchingFilters = this.getCurrentFilterState();
      this.showSelectAllPrompt = false;
      // Clear any previous deselected items and persistent selections
      this.deselectedIds.clear();
      this.persistentSelectedIds.clear();
      this.updateBulkActionButtons();
    },

    cancelSelectAllMatching() {
      this.showSelectAllPrompt = false;
      // Keep current page selection
      this.selectionMode = 'page';
    },

    clearAllSelections() {
      // Clear all selections and reset state
      this.selectionMode = 'none';
      this.showSelectAllPrompt = false;
      this.allMatchingFilters = null;
      this.persistentSelectedIds.clear();
      this.deselectedIds.clear();
      if (this.$refs.pricingDashboard?.tabulator) {
        this.$refs.pricingDashboard.tabulator.deselectRow();
      }
      this.updateBulkActionButtons();
    },

    hasFilterStateChanged(currentState) {
      // Compare current filter state with last known state
      if (!this.lastFilterState) {
        // First time - store current state and assume no change
        this.lastFilterState = currentState;
        return false;
      }

      // Compare the filter states
      const statesEqual = JSON.stringify(this.lastFilterState) === JSON.stringify(currentState);
      return !statesEqual;
    },

    getCurrentFilterState() {
      // Capture current tabulator filters, sort, and date range
      const tabulator = this.$refs.pricingDashboard?.tabulator;
      if (!tabulator) return null;

      try {
        // Create serializable versions of filters and sorters
        const filters = tabulator.getFilters().map(filter => ({
          field: filter.field,
          type: filter.type,
          value: filter.value
        }));

        const sorters = tabulator.getSorters().map(sorter => ({
          field: sorter.field,
          dir: sorter.dir
        }));

        // Format date range as Unix timestamps (exactly as backend expects)
        const dateRange = this.range && this.range.start && this.range.end ? {
          start: new Date(this.range.start).getTime(), // Backend converts to Unix timestamp itself
          end: new Date(this.range.end).getTime()      // Backend converts to Unix timestamp itself
        } : null;

        return {
          filters: filters,
          sort: sorters,
          dateRange: dateRange,  // Nested dateRange (as backend expects)
          searchTerm: null
        };
      } catch (error) {
        console.warn('Error getting filter state:', error);
        return {
          filters: [],
          sort: [],
          dateRange: { ...this.range },
          searchTerm: null
        };
      }
    },

    clearAllSelections() {
      this.selectionMode = 'none';
      this.allMatchingFilters = null;
      this.selectedPageItems = [];
      this.totalMatchingCount = 0;
      this.persistentSelectedIds.clear(); // Clear persistent selections
      if (this.$refs.pricingDashboard?.tabulator) {
        this.$refs.pricingDashboard.tabulator.deselectRow();
      }
      this.updateBulkActionButtons();
    },

    restoreSelections() {
      if (!this.$refs.pricingDashboard?.tabulator) {
        return;
      }

      // Set flag to prevent processSelectionCount from interfering
      this.isRestoringSelections = true;

      // Use setTimeout to ensure the tabulator has finished rendering the new data
      setTimeout(() => {
        if (!this.$refs.pricingDashboard?.tabulator) {
          this.isRestoringSelections = false;
          return;
        }

        const currentData = this.$refs.pricingDashboard.tabulator.getData();

        if (this.selectionMode === 'all-matching') {
          // In "all-matching" mode, select all rows EXCEPT deselected ones
          if (this.deselectedIds.size === 0) {
            // No deselected items, select all rows
            this.$refs.pricingDashboard.tabulator.selectRow();
          } else {
            // Select all rows except deselected ones
            const rowsToSelect = currentData
              .filter(row => !this.deselectedIds.has(row.swsd_id))
              .map(row => row.swsd_id);

            if (rowsToSelect.length > 0) {
              this.$refs.pricingDashboard.tabulator.selectRow(rowsToSelect);
            }
          }

          // Verify selection worked and clear flag
          setTimeout(() => {
            this.isRestoringSelections = false;
          }, 50);

        } else if (this.persistentSelectedIds.size > 0) {
          // Restore selections for rows that are currently visible and were previously selected
          console.log('🟢 RESTORING PERSISTENT SELECTIONS');
          console.log('🟢 Total persistent IDs:', this.persistentSelectedIds.size);
          console.log('🟢 Current page data rows:', currentData.length);
          console.log('🟢 Sample persistent IDs:', Array.from(this.persistentSelectedIds).slice(0, 5));
          console.log('🟢 Sample current page IDs:', currentData.slice(0, 5).map(row => row.swsd_id));

          const idsToSelect = currentData
            .filter(row => this.persistentSelectedIds.has(row.swsd_id))
            .map(row => row.swsd_id);

          console.log('🟢 IDs to select on this page:', idsToSelect.length);
          console.log('🟢 First 5 IDs to select:', idsToSelect.slice(0, 5));

          if (idsToSelect.length > 0) {
            this.$refs.pricingDashboard.tabulator.selectRow(idsToSelect);
          }
          this.isRestoringSelections = false;
        } else {
          this.isRestoringSelections = false;
        }
      }, 100); // Small delay to ensure data is rendered
    },

    getSelectedCount() {
      if (this.selectionMode === 'all-matching') {
        return this.totalMatchingCount;
      } else if (this.selectionMode === 'page' || this.persistentSelectedIds.size > 0) {
        // Return total persistent selections across all pages
        return this.persistentSelectedIds.size;
      }
      return 0;
    },

    getActionTooltip(action) {
      const count = this.getSelectedCount();
      const scope = this.selectionMode === 'all-matching' ? 'all matching items' : 'selected items across pages';
      return `${action} ${count} ${scope}`;
    },

    async processBulkActionPersistentSelections(action, params) {
      // Fetch the selected rows from server using their IDs
      const selectedIds = Array.from(this.persistentSelectedIds);

      try {
        const response = await fetch('/node/services/get-services-by-ids', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ids: selectedIds })
        });

        const selectedRows = await response.json();

        // Process the rows based on action
        if (action === 'approve') {
          let validRows = [];
          let failedRows = [];

          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              validRows.push(sR);  // Rows that pass the conditions
            } else {
              failedRows.push(sR);  // Rows that fail the conditions
            }
          });

          await this.acceptUserChangesNew(validRows, failedRows, params.status);
        }
        // Add other actions as needed

      } catch (error) {
        console.error('Error processing bulk action:', error);
        this.$refs.pricingDashboard.showAlert('Error processing bulk action', 'error');
      }
    },

    updateBulkActionButtons() {
      // Update button states based on selection mode
      const hasSelection = this.selectionMode !== 'none';
      this.emailButtonDisabled = !hasSelection;
      this.invoiceButtonDisabled = !hasSelection;
    },

    async processBulkActionAllMatching(action, actionParams = {}) {
      // Validate inputs
      if (!action || !this.allMatchingFilters) {
        this.showErrorDialog('Invalid operation parameters');
        return;
      }

      // Safety limit check
      if (this.totalMatchingCount > 15000) {
        this.showErrorDialog(`Operation too large: ${this.totalMatchingCount} items exceeds maximum limit of 15,000`);
        return;
      }

      // Show confirmation for large operations
      if (this.totalMatchingCount > 1000) {
        const confirmed = confirm(`This will ${action} ${this.totalMatchingCount} items. This may take a while. Continue?`);
        if (!confirmed) return;
      }

      // Show warning for very large operations
      if (this.totalMatchingCount > 5000) {
        const confirmed = confirm(`WARNING: This operation will affect ${this.totalMatchingCount} items and may take several minutes. Are you sure you want to continue?`);
        if (!confirmed) return;
      }

      // Check if another bulk operation is in progress
      if (this.bulkActionProgress.show) {
        this.showErrorDialog('Another bulk operation is already in progress. Please wait for it to complete.');
        return;
      }

      // Initialize progress dialog
      this.bulkActionProgress = {
        show: true,
        action: this.getActionDisplayName(action),
        percentage: 0,
        processed: 0,
        total: this.totalMatchingCount,
        results: { success: [], failed: [] },
        startTime: Date.now(),
        estimatedTimeRemaining: null
      };

      try {
        // Simulate progress updates for better UX
        const progressInterval = setInterval(() => {
          if (this.bulkActionProgress.percentage < 90) {
            this.bulkActionProgress.percentage += Math.random() * 10;
            this.updateEstimatedTime();
          }
        }, 500);

        // Set up timeout for very large operations
        const timeoutMs = Math.max(30000, this.totalMatchingCount * 10); // 10ms per item, minimum 30s
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
        }, timeoutMs);

        // Create a clean, serializable copy of the filter state
        const cleanFilterState = this.allMatchingFilters ? JSON.parse(JSON.stringify(this.allMatchingFilters)) : null;
        console.log('🔍 Sending bulk action request with filterState:', cleanFilterState);

        const response = await fetch('/node/services/bulk-action-all-matching', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: action,
            actionParams: actionParams,
            selectionMode: 'all-matching',
            filterState: cleanFilterState,
            maxItems: 10000 // Safety limit
          }),
          signal: controller.signal
        });

        clearInterval(progressInterval);
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        // Update progress to completion
        this.bulkActionProgress.percentage = 100;
        this.bulkActionProgress.processed = result.processedCount || 0;
        this.bulkActionProgress.results = result.results || { success: [], failed: [] };
        this.bulkActionProgress.estimatedTimeRemaining = 0;

        // Show completion message
        this.showBulkActionSummary(action, result);

        // Refresh the table data
        await this.fetchData();

        // Clear selection after successful operation
        this.clearAllSelections();

      } catch (error) {
        console.error('Bulk action error:', error);
        this.bulkActionProgress.show = false;

        if (error.name === 'AbortError') {
          this.showErrorDialog(`Operation timed out after ${Math.round(timeoutMs/1000)} seconds. The operation may still be processing on the server.`);
        } else if (error.message.includes('Failed to fetch')) {
          this.showErrorDialog('Network error: Please check your connection and try again.');
        } else {
          this.showErrorDialog(`Error processing bulk action: ${error.message}`);
        }

        // Refresh data in case some items were processed
        try {
          await this.fetchData();
        } catch (refreshError) {
          console.error('Error refreshing data after bulk action error:', refreshError);
        }
      }
    },

    getActionDisplayName(action) {
      const actionNames = {
        'approve': 'Approving Services',
        'reject': 'Rejecting Services',
        'updateSnow': 'Updating Snow Totals',
        'updatePeople': 'Updating People Count',
        'updateHours': 'Updating Hours'
      };
      return actionNames[action] || action;
    },

    updateEstimatedTime() {
      const elapsed = Date.now() - this.bulkActionProgress.startTime;
      const progress = this.bulkActionProgress.percentage / 100;
      if (progress > 0.1) {
        const totalEstimated = elapsed / progress;
        const remaining = totalEstimated - elapsed;
        this.bulkActionProgress.estimatedTimeRemaining = Math.max(0, Math.round(remaining / 1000));
      }
    },

    showBulkActionSummary(action, result) {
      const successCount = result.processedCount || 0;
      const failedCount = result.failedCount || 0;

      let message = `${this.getActionDisplayName(action)} Complete`;
      let details = [];

      if (successCount > 0) {
        details.push(`✓ ${successCount} items processed successfully`);
      }
      if (failedCount > 0) {
        details.push(`✗ ${failedCount} items failed`);
      }

      // Update the progress dialog with final results
      setTimeout(() => {
        this.showInfoDialog(message, details.join('\n'), '');
      }, 1000);
    },

    showErrorDialog(message) {
      this.showInfoDialog('Error', '', message);
    },

    openPostdatedDashboard(){
      this.dialogPostdatedDashboard = true;
    },
    resetDate() {
      this.dateRangePicker.range = null;
    },
    showInfoDialog(actionPerformed,actionSuccess, actionFailed){
      this.info.actionPerformed = actionPerformed;
      this.info.successRows = actionSuccess;
      this.info.failedRows = actionFailed;
      this.info.vDialog = true;
    },
    openPricingContractForContractor(e, row) {
      let rowData = row.getData();
      if (rowData.TradeIdContractor !== -1){
        this.pricingProps.buildingId = rowData.site_id;
        this.pricingProps.pricingType = rowData.PricingTypeContractor;
        this.pricingProps.parentId = rowData.ParentIdContractor;
        this.pricingProps.tradeId = rowData.TradeIdContractor;
        this.pricingProps.copyMode = false;
        this.pricingProps.show = true;
      }
    },
    openPricingContract(e, row) {
      let rowData = row.getData();
      this.pricingProps.buildingId = rowData.site_id;
      this.pricingProps.pricingType = rowData.PricingType;
      this.pricingProps.parentId = rowData.ParentId;
      this.pricingProps.tradeId = rowData.TradeId;
      this.pricingProps.copyMode = false;
      this.pricingProps.show = true;
    },
    onComponentFetchDone() {
      this.pricingProps.componentFetchDone = true;
    },
    onCancel: async function () {
      this.pricingProps = {
        tradeId: 0,
        parentId: 0,
        buildingId: 0,
        show: false,
        loaded: true,
        pricingType: '',
        contractType: 'Snow Removal',
        pricingJson: {},
        componentFetchDone: false,
        copyMode: false
      }
    },
    parseCollapsedRows(selectedRows) {
      if (this.tableCollapsed) {
        // If view is collapsed, use the stored uncollapsed data for the current page
        let dataToFilter = this.currentPageUncollapsedData;
        if (!dataToFilter || dataToFilter.length === 0) {
          // Fallback or error handling if currentPageUncollapsedData is empty
          // This might happen if collapseTable wasn't called or data wasn't populated.
          // For now, returning the selectedRows as is, or you might want to return an empty array
          // or show an error, depending on desired behavior.
          console.warn("currentPageUncollapsedData is empty in parseCollapsedRows while table is collapsed.");
          return selectedRows; // Or handle more gracefully
        }
        let selectedRowsIds = selectedRows.map(od => od.FormId);
        // Filter the current page's uncollapsed data to get the full objects for selected FormIds
        let filteredData = dataToFilter.filter(od => selectedRowsIds.includes(od.FormId));
        return filteredData;
      } else {
        // If table is not collapsed, selectedRows already contains the full data objects
        return selectedRows;
      }
    },

    getServiceChannelURL(id) {
      return `https://www.servicechannel.com/sc/wo/Workorders/index?id=${id}`
    },
    async collapseTable() {
      this.tableCollapsed = !this.tableCollapsed;
      let currentTableData = this.$refs.pricingDashboard.tabulator.getData("active"); // Get current page data

      if (this.tableCollapsed) {
        // Store the current page's uncollapsed data before converting
        this.currentPageUncollapsedData = [...currentTableData]; // Use spread to clone
        let dataTreeStructure = this.convertDataToRooferStructure(currentTableData);
        this.$refs.pricingDashboard.tabulator.replaceData(dataTreeStructure); // Replace current view
        this.$refs.pricingDashboard.showAlert("Table is now in collapsed view (current page data)", "success");
      } else {
        // Clear the stored uncollapsed data when expanding
        this.currentPageUncollapsedData = [];
        // To revert, we need to re-fetch from server or restore previous page data if cached
        // For simplicity now, just re-trigger a fetch for the current page parameters
        // This might not preserve the exact "uncollapsed" state if filters/sort changed,
        // but it's a starting point for server-side data.
        this.$refs.pricingDashboard.tabulator.setData();
        this.$refs.pricingDashboard.showAlert("Table is now in expanded view", "success");
      }
    },

    convertDataToRooferStructure(data) { // This now operates on page data if table is collapsed
      const formIds = Array.from(new Set(data.map(dt => dt.FormId)));
      const finalArray = formIds.map(formId => {
        const perFormArray = data.filter(dt => dt.FormId === formId);

        const combinedObject = perFormArray.reduce((acc, curr) => {
          for (const key in curr) {
            if (acc.hasOwnProperty(key)) {
              if (key === 'Service' || key === 'Notes') {
                acc[key] += `${acc[key] ? ', ' : ''}${curr[key]}`;
              } else if (key === 'ContractorPricing' || key === 'ClientPricing' || key === 'snow_inch' || key === 'People' || key === 'Hours') {
                acc[key] += curr[key]
              }
            } else {
              acc[key] = curr[key];
            }
          }
          return acc;
        }, {});

        return combinedObject;
      });
      return finalArray;
    },
    isStringEmptyOrWhitespace(str) {
      return str.trim().length === 0;
    },
    //Update add service related notes
    async addUpdateServiceNotes(rowData, notes) {
      let serviceId = rowData.swsd_id;
      await fetch(`/node/services/service-notes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          serviceId: serviceId,
          notes: notes
        })
      });
      this.$refs.pricingDashboard.showAlert("Notes have been updated", "success");
    },
    //FORM SUBMIT RELATED METHODS START
    _formSubmit: function (event) {
      this.formSubmitDialog = true;
      this.siteIDForFormSubmit = 0;
    },
    closeFormSubmit: async function (shouldRefreshData) {
      this.formSubmitDialog = false;
      await this.fetchData();
    },
    //FORM SUBMIT RELATED METHODS END

    async applyEventBulk(singleUpdate = false, singleRow = []) {
      if (!this.eventName || this.eventName.length < 4) {
        alert("Event Name should contain at least 4 characters!");
        return;
      }
      try {
        let ids = [];
        let validRows = [];
        let failedRows = [];
        let rows = [];
        let selectedRows = [];

        if (singleUpdate && singleRow.length && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          validRows = singleRow; // If it's a single update, assume it's valid
        } else {
          this.eventApplyLoading = true;
          rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              eventName: this.eventName
            })
          });
          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          //Let's not fetch data from server, instead manipulate front-end
          this.newData = validRows.map(item => ({...item, swsd_event: this.eventName}))
          await this.updateData(this.newData);
          //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, swsd_event: this.eventName})) );

          // Show the result dialog
          if (validRows.length > 0 || failedRows.length > 0) {
            let message = 'Event Assignment';
            let successMessage = '';
            let failureMessage = '';

            if (validRows.length > 0) {
              successMessage += `\n${validRows.length} service(s) updated successfully.`;
            }
            if (failedRows.length > 0) {
              failureMessage += `\n${failedRows.length} service(s) is/are locked and cannot be updated.`;
            }

            this.showInfoDialog(message, successMessage, failureMessage);
          }

        } else {
          // Show a message if no valid services are selected
          this.showInfoDialog('Service Update Summary', '', 'No services were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.eventApplyLoading = false;
        this.eventApplyDialog = false;
        this.eventName = null;
      }
    },
    async applySnowTotalBulk(singleUpdate = false, singleRow = [], snowTPassed = 0) {

      if (isNaN(this.snowTotal) && singleUpdate == false) {
        alert("Snow Total should be a number");
        return;
      }
      try {
        this.snowTotalApplyLoading = true;
        let snowTotal = this.snowTotal;

        if (singleUpdate && singleRow[0].VendorId == myvtem) {
          snowTotal = snowTPassed;
          if (isNaN(snowTotal)) {
            alert("Snow Total should be a number");
            return;
          }
          // Handle single update (existing logic)
          let ids = [singleRow[0].swsd_id];
          let validRows = [singleRow[0]];
          await this.processSingleSnowUpdate(ids, validRows, snowTotal);
        } else if (this.selectionMode === 'all-matching') {
          // Handle bulk operation on all matching items
          await this.processBulkActionAllMatching('updateSnow', { snowTotal });
        } else {
          // Handle current page selection
          let ids = [];
          let validRows = [];
          let failedRows = [];
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
          await this.processSingleSnowUpdate(ids, validRows, snowTotal, failedRows);
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.snowTotalApplyLoading = false;
        this.snowApplyDialog = false;
        this.snowTotal = null;
      }
    },

    async processSingleSnowUpdate(ids, validRows, snowTotal, failedRows = []) {
      if (ids.length > 0) {
        this.getDataLoader = true;
        const response = await fetch(`/node/services/update-submitted-service`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            ids: ids,
            snowTotal: snowTotal
          })
        });
        this.getDataLoader = false;
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        let newData = validRows.map(item => ({...item, snow_inch: snowTotal}))
        await this.updateData(newData);

        // Show the result dialog
        let message = 'Snow Total Update Summary';
        let successMessage = '';
        let failureMessage = '';

        if (validRows.length > 0) {
          successMessage += `\n${validRows.length} service(s) snow total(s) updated successfully.`;
        }
        if (failedRows.length > 0) {
          failureMessage += `\n${failedRows.length} service(s) snow total(s) is/are locked and cannot be updated.`;
        }

        this.showInfoDialog(message, successMessage, failureMessage);
      } else {
        // Show a message if no valid snow totals are selected
        this.showInfoDialog('Snow Total Update Summary', '', 'No snow totals were eligible for updating.');
      }
    },
    async updateData(data) {

      // This method's reliance on this.originalData needs to change for server-side paging.
      // For now, after a server update, we will re-fetch the current page.
      // Individual row updates can be done with tabulator.updateData([{id:..., ...}])
      // but a full page refresh is simpler after bulk operations.
      if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
          this.$refs.pricingDashboard.tabulator.setData(); // Re-trigger AJAX call for current page
      }
      // The old logic of manipulating this.originalData is removed.
    },
    async applyPeopleToBulk(singleUpdate = false, singleRow = [], peoplePassed = 0) {
      if (isNaN(this.peopleTotal)) {
        alert("People should be a number");
        return;
      }
      try {
        this.peopleTotalApplyLoading = true;
        let ids = [];
        let people = this.peopleTotal;
        let validRows = [];
        let failedRows = [];

        if (singleUpdate && singleRow.length == 1 && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          people = peoplePassed;
          validRows = [singleRow[0]]; // Assuming single update is valid
        } else {
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              people: people
            })
          });

          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          let newData = validRows.map(item => ({...item, People: people}))
          await this.updateData(newData);
          // Show the result dialog
          let message = 'People Update Summary';
          let successMessage = '';
          let failureMessage = '';

          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) people total(s) updated successfully.`;
          }
          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) people total(s) is/are locked and cannot be updated.`;
          }

          this.showInfoDialog(message, successMessage, failureMessage);

        } else {
          // Show a message if no valid people totals are selected
          this.showInfoDialog('People Update Summary', '', 'No people totals were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.peopleTotalApplyLoading = false;
        this.peopleApplyDialog = false;
        this.peopleTotal = null;
      }
    },

    async applyHoursToBulk(singleUpdate = false, singleRow = [], hoursPassed = 0) {
      if (isNaN(this.hoursTotal)) {
        alert("Hours should be a number");
        return;
      }
      try {
        this.hoursTotalApplyLoading = true;
        let ids = [];
        let hours = this.hoursTotal;
        let validRows = [];
        let failedRows = [];

        if (singleUpdate && singleRow.length == 1 && singleRow[0].VendorId == myvtem) {
          ids = [singleRow[0].swsd_id];
          hours = hoursPassed;
          validRows = [singleRow[0]]; // Assuming single update is valid
        } else {
          const rows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let selectedRows = this.parseCollapsedRows(rows);
          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              ids.push(sR.swsd_id);
              validRows.push(sR);
            } else if (sR.Status === 'LOCKED') {
              failedRows.push(sR);
            }
          });
        }

        if (ids.length > 0) {
          this.getDataLoader = true;
          const response = await fetch(`/node/services/update-submitted-service`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              ids: ids,
              hours: hours
            })
          });
          this.getDataLoader = false;
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          this.newData = validRows.map(item => ({...item, Hours: hours}))
          //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, Hours: hours})) );
          await this.updateData(this.newData);
          // Show the result dialog
          let message = 'Hours Update Summary';
          let successMessage = '';
          let failureMessage = '';

          if (validRows.length > 0) {
            successMessage += `\n${validRows.length} service(s) hour(s) updated successfully.`;
          }
          if (failedRows.length > 0) {
            failureMessage += `\n${failedRows.length} service(s) hour(s) is/are locked and cannot be updated.`;
          }

          this.showInfoDialog(message, successMessage, failureMessage);

        } else {
          // Show a message if no valid hours are selected
          this.showInfoDialog('Hours Update Summary', '', 'No hours were eligible for updating.');
        }

      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.hoursTotalApplyLoading = false;
        this.hoursApplyDialog = false;
        this.hoursTotal = null;
      }
    },

    processSelectionCount() {
      // Don't interfere if we're in the process of restoring selections
      if (this.isRestoringSelections) {
        return;
      }

      const selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      const filteredSelectedRows = selectedRows.filter(row => row.VendorId == myvtem); // Filter rows and remove subcontracted rows.

      // Update persistent selection tracking (only if not in all-matching mode)
      if (this.selectionMode !== 'all-matching') {
        const currentPageIds = this.$refs.pricingDashboard.tabulator.getData().map(row => row.swsd_id);
        const selectedIds = selectedRows.map(row => row.swsd_id);

        // Remove current page IDs from persistent set, then add selected ones
        currentPageIds.forEach(id => this.persistentSelectedIds.delete(id));
        selectedIds.forEach(id => this.persistentSelectedIds.add(id));

      }

      // Update selection state
      this.selectedPageItems = selectedRows;

      // Determine selection mode
      const totalRowsOnPage = this.$refs.pricingDashboard.tabulator.getRows("active").length;
      const allPageRowsSelected = selectedRows.length === totalRowsOnPage && totalRowsOnPage > 0;

      if (this.selectionMode === 'all-matching') {
        // If in all-matching mode but user manually deselected some items, track deselected items
        if (!allPageRowsSelected && selectedRows.length < totalRowsOnPage) {
          // Find which items were deselected on this page
          const currentPageData = this.$refs.pricingDashboard.tabulator.getData();
          const selectedIds = new Set(selectedRows.map(row => row.swsd_id));
          const deselectedOnThisPage = currentPageData
            .filter(row => !selectedIds.has(row.swsd_id))
            .map(row => row.swsd_id);

          // Add deselected items to our deselected set
          deselectedOnThisPage.forEach(id => this.deselectedIds.add(id));

          // Don't trigger select all prompt when we just deselected items
          this.showSelectAllPrompt = false;
        }
        // Stay in all-matching mode - don't change it
      } else {
        // Normal selection mode logic
        if (selectedRows.length === 0 && this.persistentSelectedIds.size === 0) {
          this.selectionMode = 'none';
          this.allMatchingFilters = null;
        } else if (allPageRowsSelected && !this.showSelectAllPrompt) {
          // All rows on current page are selected - show prompt for "select all matching"
          // Only show if we're not already showing the prompt AND not already in all-matching mode
          this.selectionMode = 'page';
          // Trigger the select all matching prompt
          this.$nextTick(() => {
            this.checkForSelectAllPrompt();
          });
        } else {
          this.selectionMode = 'page';
        }
      }

      this.updateBulkActionButtons();
    },

    checkForSelectAllPrompt() {
      // Don't show prompt if we're in the process of restoring selections
      if (this.isRestoringSelections) {
        return;
      }

      // Don't show prompt if we're already in all-matching mode
      if (this.selectionMode === 'all-matching') {
        console.log('🟡 Not showing select all prompt - already in all-matching mode');
        return;
      }

      const selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      const totalRowsOnPage = this.$refs.pricingDashboard.tabulator.getRows("active").length;
      const allPageRowsSelected = selectedRows.length === totalRowsOnPage && totalRowsOnPage > 0;

      // Only show prompt if:
      // 1. All page rows are selected
      // 2. We're in 'page' mode (not 'all-matching' or 'none')
      // 3. Prompt is not already showing
      // 4. There are more total items than what's on the current page
      const totalCount = this.totalMatchingCount || totalRowsOnPage;
      const hasMoreItems = totalCount > totalRowsOnPage;

      if (allPageRowsSelected && this.selectionMode === 'page' && !this.showSelectAllPrompt && hasMoreItems) {
        console.log('🟡 Showing select all prompt');
        this.onSelectAllMatchingPrompt({
          pageCount: totalRowsOnPage,
          totalCount: totalCount
        });
      }
    },
    async approveServices(status) {
      this.approveServicesLoading = true;

      if (this.selectionMode === 'all-matching') {
        // Handle bulk operation on all matching items
        await this.processBulkActionAllMatching('approve', { status });
      } else {
        // Handle persistent selections across pages
        if (this.persistentSelectedIds.size > 0) {
          await this.processBulkActionPersistentSelections('approve', { status });
        } else {
          // Fallback to current page selection if no persistent selections
          let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
          let validRows = [];
          let failedRows = [];

          selectedRows.forEach(sR => {
            if (sR.VendorId == myvtem && sR.Status != 'LOCKED') {
              validRows.push(sR);  // Rows that pass the conditions
            } else {
              failedRows.push(sR);  // Rows that fail the conditions
            }
          });
          await this.acceptUserChangesNew(validRows, failedRows, status);
        }
      }

      this.approveServicesLoading = false;
    },
    async acceptUserChangesNew(validRows, failedRows, status) {
      let resp = await fetch('/node/services/service-status', {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "rows": validRows,
          "status": status
        })
      });
      this.approveServicesLoading = false;
      this.newData = validRows.map(item => ({...item, Status: status}))
      await this.updateData(this.newData);
      //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, Status: status})) );

      if (status == 'APPROVED') {
        let message = 'Service Approval Summary';
        let successMessage = '';
        let failureMessage = '';
        if (validRows.length > 0) {
          successMessage += `\n${validRows.length} service(s) approved successfully.`;
        }
        if (failedRows.length > 0) {
          failureMessage += `\n${failedRows.length} service(s) is/are locked and cannot be approved, as action is restricted for locked services.`;
        }
        this.showInfoDialog('Service Approval Summary', successMessage, failureMessage);
      }
      else if (status == 'LOCKED') {
        this.showInfoDialog('Service Lock Summary', `${validRows.length} services have been locked successfully.`, '');
      }
    },

    async createBill() {
      try {
        let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;

        if (selectedRows.length < 1) {
          this.$set(this.snackbar, 'text', "Please select some rows");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        //check if the row has invoice already if yes do not allow to reinvoice
        let billedRows = selectedRows.filter((sr) => sr.swsd_qb_bill_id != null && sr.swsd_qb_bill_id !== '' && sr.Status != 'LOCKED');

        if (billedRows.length > 0) {
          this.$set(this.snackbar, 'text', "Please select rows that are not already billed");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        let priceData = selectedRows.map(({ ContractorPricing }) => ContractorPricing);
        let isNumeric = priceData.every((price) => price === "" || price == 0 ? false : !isNaN(price));

        if (!isNumeric) {
          this.$set(this.snackbar, 'text', "Please select rows with contractor pricing");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        let contractorDump = {};


        selectedRows.forEach(({ swsd_id, swsd_service_id, ContractorPricingUi, Service, UnixTime, ProviderContactId, ContractorData, mb_quickbooks_customer_id, swsd_vendor_sequence_id }) => {
          const customer = this.clients.find((client) => client.sqc_id == mb_quickbooks_customer_id)?.sqc_external_id;

          if (!contractorDump[ProviderContactId]) {
            contractorDump[ProviderContactId] = [];
          }
          contractorDump[ProviderContactId].push({
            swsd_id: swsd_id,
            contractor_name: ContractorData?.company,
            customer_id: customer,
            s_id: swsd_service_id,
            total: ContractorPricingUi,
            service_name: Service,
            service_index: swsd_vendor_sequence_id,
            date: moment.unix(UnixTime).format("MM/DD/YYYY HH:mm")
          });
        });

        let uniqueContractorIds = Object.keys(contractorDump);
        if (uniqueContractorIds.length === 0) {
          this.$set(this.snackbar, 'text', "Please select rows for contractors associated with QuickBooks");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        this.billLoading = true;
        this.billProgress = 0;
        this.billDialog = true;
        this.billResults = [];

        for (let i = 0; i < uniqueContractorIds.length; i++) {
          let contractorId = uniqueContractorIds[i];
          let data = contractorDump[contractorId];

          let contractorName = data[0].contractor_name;
          try {
            let response = await fetch(`/node/quickbooks/bill/${contractorId}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data),
            });

            if (response.ok) {
              const res = await response.json();
              if (res.status === 'success')
                this.billResults.push({ contractorId, contractorName, message: res.message, status: 'success' });
              else
                this.billResults.push({ contractorId, contractorName, message: res.message, status: 'error' });
            } else {
              this.billResults.push({ contractorId, contractorName, message: "Error creating bill in QuickBooks", status: 'error' });
            }
          } catch (error) {
            console.error("Error in createBill for contractorId", contractorId, error);
            this.billResults.push({ contractorId, contractorName, message: "An unexpected error occurred", status: 'error' });
          }

          this.billProgress = ((i + 1) / uniqueContractorIds.length) * 100;
        }
        await this.fetchData();
        this.billLoading = false;
      } catch (error) {
        console.error("Error in createBill", error);
        this.$set(this.snackbar, 'text', "An unexpected error occurred");
        this.$set(this.snackbar, 'snackbar', true);
        this.billLoading = false;
        this.billDialog = false;
      }
    },

    async createInvoice() {
      try {
        let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;

        if (selectedRows.length < 1) {
          this.$set(this.snackbar, 'text', "Please select some rows");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        //check if the row has invoice already if yes do not allow to reinvoice, ALSO CHECK SERVICE IS NOT LOCKED
        let invoicedRows = selectedRows.filter((sr) => sr.swsd_qb_invoice_id != null && sr.swsd_qb_invoice_id !== '' && sr.Status != 'LOCKED');

        if (invoicedRows.length > 0) {
          this.$set(this.snackbar, 'text', "Please select rows that are not invoiced");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }
        //Following logic will fail for per event becuase there
        let priceData = selectedRows.map(({ ClientPricing }) => ClientPricing);
        let isNumeric = priceData.every((price) => price === "" || price == 0 ? false : !isNaN(price));
        if (!isNumeric) {
          this.$set(this.snackbar, 'text', "Please select rows with client pricing");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        let clientData = {};
        selectedRows.forEach(({ swsd_id, swsd_service_id, ClientPricingUi, Service, UnixTime, mb_quickbooks_customer_id, People, Hours, swsd_vendor_sequence_id }) => {
          if (!mb_quickbooks_customer_id || mb_quickbooks_customer_id === "" || mb_quickbooks_customer_id === "null") {
            return;
          }

          if (!clientData[mb_quickbooks_customer_id]) {
            clientData[mb_quickbooks_customer_id] = [];
          }
          const client_name = this.clients.find((client) => client.sqc_id == mb_quickbooks_customer_id);
          const qty = People && Hours ? People * Hours : 1;
          clientData[mb_quickbooks_customer_id].push({
            client_name: client_name?.sqc_display_name,
            s_id: swsd_service_id,
            total: ClientPricingUi,
            swsd_id: swsd_id,
            service_name: Service,
            service_index: swsd_vendor_sequence_id,
            date: moment.unix(UnixTime).format("MM/DD/YYYY HH:mm"),
            qty: qty
          });
        });

        let uniqueClientIds = Object.keys(clientData);
        if (uniqueClientIds.length === 0) {
          this.$set(this.snackbar, 'text', "There are no customers associated with the rows");
          this.$set(this.snackbar, 'snackbar', true);
          return;
        }

        this.invoiceLoading = true;
        this.invoiceProgress = 0;
        this.invoiceDialog = true;
        this.invoiceResults = [];

        for (let i = 0; i < uniqueClientIds.length; i++) {
          let clientId = uniqueClientIds[i];
          let data = clientData[clientId];
          let clientName = data[0]?.client_name;

          try {
            let response = await fetch(`/node/quickbooks/invoice-site-customer/${clientId}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data),
            });

            if (response.ok) {
              const invoiceNumber = await response.json();
              if (invoiceNumber.status === 'success')
                this.invoiceResults.push({ clientId, clientName, message: invoiceNumber.message, status: 'success' });
              if (invoiceNumber.status === 'error')
                this.invoiceResults.push({ clientId, clientName, message: invoiceNumber.message, status: 'error' });
            } else {
              this.invoiceResults.push({ clientId, clientName, message: invoiceNumber.message, status: 'error' });
            }
          } catch (error) {
            console.error("Error in createInvoice for clientId", clientId, error);
            this.invoiceResults.push({ clientId, clientName, message: "An unexpected error occurred", status: 'error' });
          }

          this.invoiceProgress = ((i + 1) / uniqueClientIds.length) * 100;
        }
        await this.fetchData();
        this.invoiceLoading = false;
      } catch (error) {
        console.error("Error in createInvoice", error);
        this.$set(this.snackbar, 'text', "An unexpected error occurred");
        this.$set(this.snackbar, 'snackbar', true);
        this.invoiceLoading = false;
        this.invoiceDialog = false;
      }
    },

    async rejectServices() {
      let message = 'Service Processing Summary';
      let successMessage = '';
      let failureMessage = '';

      // Filtering valid and failed rows
      let validRows = [];
      let failedRows = [];

      let selectedRows = this.$refs.pricingDashboard.tabulator.getSelectedData();
      selectedRows.forEach(sR => {
        if (sR.VendorId == myvtem &&
          sR.Status !== 'APPROVED' &&
          sR.Status !== 'INVOICED' &&
          sR.Status !== 'BILLED' &&
          sR.Status !== 'INVOICED_BILLED' &&
          sR.Status !== 'LOCKED') {
          validRows.push(sR);  // Rows that meet the conditions
        } else {
          failedRows.push(sR);  // Rows that fail the conditions
        }
      });

      // Generating success and failure messages
      if (validRows.length > 0) {
        successMessage += `\n${validRows.length} service(s) rejected successfully.`;
      }

      if (failedRows.length > 0) {
        failureMessage += `\n${failedRows.length} service(s) could not be rejected due to one of the following statuses: Approved, Invoiced, Billed, or Locked.`;
      }

      if (validRows.length > 0) {
        this.rejectServicesLoading = true;
        await fetch('/node/services/service-status', {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            "rows": validRows,
            "status": "REJECTED"
          })
        });
        this.rejectServicesLoading = false;
        this.newData = validRows.map(item => ({...item, Status: "REJECTED"}))
        await this.updateData(this.newData);
        //this.$refs.pricingDashboard.tabulator.updateData( validRows.map(item => ({...item, Status: "REJECTED"})) );

        this.showInfoDialog('Service Processing Summary', successMessage, failureMessage);
      }
      else {
        this.$refs.pricingDashboard.showAlert("Services cannot be rejected.", "success");
      }
    },
    async sendBreadcrumb(type, date, accessCode, siteId, profileID, uuid, contractorEmail) {
      let params = new URLSearchParams({
        'type': type,
        'accessCode': accessCode,
        'email': contractorEmail,
        'dt': date,
        'deviceType': 'WEB',
        'deviceModel': 'ADMINDASHBOARD',
        'lat': 0,
        'lon': 0,
        'uuid': uuid,
        'siteid': siteId,
        'profileID': profileID //i guess this is a form id
      });
      let request = await fetch("http://localhost/vpics/uploadbreadcrumb", {
        "headers": {
          "content-type": "application/x-www-form-urlencoded;charset=UTF-8",
        },
        "body": params,
        "method": "POST",
      });
      let data = await request.json();
    },
    isNullOrEmpty(value) {
      return value === null ||
        value === undefined ||
        (typeof value === 'string' && value.trim() === '');
    },
    async sendReportInEmail() {
      this.emailButtonLoading = true;
      let selectedRows = this.$refs.pricingDashboard.selectedRowsJson;
      selectedRows = selectedRows.filter(sr => sr.ProviderId != myvtem && sr.VendorId == myvtem && sr.Status != 'LOCKED');
      if (selectedRows.length > 0) {
        const saveTemplate = this.chBoxSaveEmailBodyAsTemplate;
        const includePricingColumn = this.chBoxIncludePricingColumn;

        if (this.tableCollapsed) {
          // If view is collapsed, use the stored uncollapsed data for the current page
          let dataToFilter = this.currentPageUncollapsedData;
          if (!dataToFilter || dataToFilter.length === 0) {
            console.warn("currentPageUncollapsedData is empty in sendReportInEmail while table is collapsed.");
            // Decide how to handle this: perhaps show an alert and return, or send email with only selected (collapsed) rows.
            // For now, proceeding with potentially empty selectedRows if dataToFilter is empty.
            // Consider adding: this.$refs.pricingDashboard.showAlert("Cannot send email for collapsed view without current page data.", "error"); return;
          }
          let selectedRowsIds = selectedRows.map(od => od.FormId);
          // Filter the current page's uncollapsed data to get the full objects for selected FormIds
          // Ensure selectedRows is reassigned with the detailed objects
          selectedRows = dataToFilter.filter(od => selectedRowsIds.includes(od.FormId));
        }


        await fetch(`/node/services/send-services-postdated`, {
          method: "post",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            selectedRows: selectedRows,
            startDate: moment(this.range.start).format('YYYY/MM/DD HH:mm:ss'),
            endDate: moment(this.range.end).format('YYYY/MM/DD HH:mm:ss'),
            checkIn: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.start).unix(),
            checkOut: this.dateRangePicker.range === null ? null : moment(this.dateRangePicker.range.end).unix(),
            saveTemplate: saveTemplate,
            includePricingColumn: includePricingColumn,
            emailBody: this.emailBody
          })
        });
        this.$refs.pricingDashboard.showAlert("Emails have been sent.", "success");
        this.sendEmailDialog = false;
        this.emailButtonLoading = false;
        this.newData = selectedRows.map(item => ({...item, Status: "SENT"}))
        await this.updateData(this.newData);
        //this.$refs.pricingDashboard.tabulator.updateData( selectedRows.map(item => ({...item, Status: "SENT"})) );
      }
      else {
        this.$refs.pricingDashboard.showAlert("Emails not sent. All services selected are read-only.", "success");
        this.sendEmailDialog = false;
        this.emailButtonLoading = false;
      }
    },
    openForm(formId) {
      window.open(`${myBaseURL}/vpics/printform?fid=${formId}`, '_blank')
    },
    navigateToRouteNewTab(name, id) {
      const routeURL = this.$router.resolve({ name: name, params: { id } }).href;
      window.open(routeURL, '_blank');
    },
    navigateToRoute(name, id) {
      this.$router.push({ name: name, params: { id } });
    },
    parseServiceDetails(value){
      if (value != null) {
        var co = phpUnserialize(value);
        if (Array.isArray(co)) {
          return co.join(", ")
        } else {
          //return co.substring(1, co.length - 1)
          return co;
        }
      }
    },
    async fetchFilterDropdownValuesInternal() { // Renamed and modified
        if (!this.range.start || !this.range.end) return;
        // this.getDataLoader = true; // Removed: Managed by fetchData
        try {
            const start = dateFns.parse(this.range.start).getTime() / 1000;
            const end = dateFns.parse(this.range.end).getTime() / 1000;
            
            const response = await fetch(`/node/services/service-history-filter-values?start=${start}&end=${end}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const filterValuesFromServer = await response.json();
            this.filterValues = filterValuesFromServer; // Store for potential direct use
        } catch (error) {
            console.error("Error fetching filter values:", error);
            if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) { // Check ref existence
                this.$refs.pricingDashboard.showAlert("Error fetching filter options.", "error");
            } else {
                console.error("pricingDashboard ref not available for filter options error alert.");
            }
            throw error; // Re-throw to be caught by Promise.all in fetchData
        }
        // finally { // Removed
        //     this.getDataLoader = false; // Removed
        // }
    },
    async fetchData() {
      this.getDataLoader = true;

      // Mark this as a filter/date change (not pagination)
      this.isFilterChange = true;

      // Reset all selections when fetching new data (filters/date range changed)
      console.log('🔄 Resetting selections due to data fetch (filters/date changed)');
      this.clearAllSelections();

      try {
        const fetchFiltersPromise = this.fetchFilterDropdownValuesInternal();

        const mainDataAndEmailPromise = async () => {
       
          if (!this.tabulatorReady) {
            this.tabulatorReady = true;
          } else {
            if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
             
              await this.$refs.pricingDashboard.tabulator.setData();
            }
          }

          // Load email data
          try {
            let emailDataResp = await fetch(`/node/services/email-data`);
            if (emailDataResp.ok) {
                const emailData = await emailDataResp.json();
                if (emailData && emailData != 0) {
                    this.emailBody = emailData.spset_email_body;
                    this.chBoxIncludePricingColumn = emailData.spset_show_pricing;
                }
            }
          } catch (e) {
            console.error("Error fetching email data", e);
           
          }
        };

        await Promise.all([fetchFiltersPromise, mainDataAndEmailPromise()]);

      } catch (error) {
        
        console.error("Error during concurrent data fetching in fetchData:", error);
        if (this.$refs.pricingDashboard && this.$refs.pricingDashboard.tabulator) {
            this.$refs.pricingDashboard.showAlert("An error occurred while loading data.", "error");
        } else {
            console.error("pricingDashboard ref not available for general data loading error alert.");
        }
      } finally {
        this.getDataLoader = false;
      }
    },
    findDuplicates(arr, keys) {
      arr.sort((a, b) => b.UnixTime - a.UnixTime);
      const seen = {};
      const duplicates = [];

      arr.forEach(item => {
        // Check if swsd_event is not null, undefined, or just spaces
        if (item.ContractorPricingUnit == 'EVENT' || item.ClientPricingUnit == 'EVENT') {
          if (item.swsd_event != null && item.swsd_event.trim() !== '') {
            const itemKeys = keys.map(key => item[key]);
            const itemKeysString = JSON.stringify(itemKeys);

            if (seen[itemKeysString]) {
              duplicates.push(item);
              duplicates.push(...seen[itemKeysString]);  // Include the previously seen item

              // Update ClientPricing for the current duplicate
              item.ClientPricingUi = 0;
              item.ContractorPricingUi = 0;
            } else {
              seen[itemKeysString] = [item];
            }
          }
        }
      });

      return duplicates;
    },
    findClosestSnowTrigger(array, snow) {
      return array.find(obj => {
        let start = obj.sps_snow_trigger_start;
        let end = obj.sps_snow_trigger_end;
        if (snow >= start && snow <= end) {
          return obj
        }
      });
    },
    getPayRateFromContractorProfile(svc, duration){
      if ( svc.ContractorPersonalProfilePricing && svc.ContractorPersonalProfilePricing.length > 0 ){
        let payRateFiltered =
          svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId
              && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id
              && eR.scsp_equipment_id === svc.equipment_id);

        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId && eR.scsp_vendor_services_service_type_id === svc.vendor_services_service_type_id);
        }
        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === svc.TradeId );
        }
        if (payRateFiltered === undefined) {
          payRateFiltered = svc.ContractorPersonalProfilePricing
            .find( eR=> eR.scsp_trade_id === 0 && eR.scsp_vendor_services_service_type_id === 0 && eR.scsp_equipment_id === 0);
        }

        if ( svc.Hours < 1 && svc.People < 1 ) {
          return payRateFiltered.scsp_price * duration.asHours();
        }
        else {
          return payRateFiltered.scsp_price * svc.Hours * svc.People;
        }
      }
    },
    convertDataToTabulatorTreeStructure(data) {
      const formIds = Array.from(new Set(data.map(dt => dt.FormId)));
      const finalArray = formIds.map(formId => {
        const perFormArray = data.filter(dt => dt.FormId === formId);
        return {
          ...perFormArray[0],
          _children: perFormArray.slice(1),
        };
      });
      return finalArray;
    },
    removeDuplicatesFromArray(array, key) {
      var uniqueValues = {};
      return array.filter(function (item) {
        var value = item[key];
        var isUnique = !uniqueValues[value];
        uniqueValues[value] = true;
        return isUnique;
      });
    },
    primaryButtonCallback() {
      this._formSubmit()
    },
    logout() {
      window.location = myBaseURL + '/index/logout'
    },
    //Below we have helper methods
    deepSearchObject(object, key, predicate) {
      if (object.hasOwnProperty(key) && predicate(key, object[key]) === true) return object

      for (let i = 0; i < Object.keys(object).length; i++) {
        let value = object[Object.keys(object)[i]];
        if (typeof value === "object" && value != null) {
          let o = this.deepSearchObject(object[Object.keys(object)[i]], key, predicate)
          if (o != null) return o
        }
      }
      return null
    },
  },

  computed: {
    vendorAccessCode() {
      return myvid;
    },
    humanizedRange() {
      let DateTime = luxon.DateTime;
      if (!this.dateRangePicker.range) return null;
      let startLuxon = DateTime.fromISO(this.dateRangePicker.range.start);
      let endLuxon = DateTime.fromISO(this.dateRangePicker.range.end);
      return `${startLuxon.toFormat('d MMM yyyy hh:mm')} - ${endLuxon.toFormat('d MMM yyyy hh:mm')}`;
    },
    //Permissions
    pagePermissions() {
      return window.userpermissions[this.pageID] || [];
    },
    pagePermissionAdmin() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Admin'); //Add/Edit/Approve/Reject,Email
      return permission ? permission['Value'] : true;
    },
    pagePermissionSuperAdmin() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'SuperAdmin'); //Super Admin
      return permission ? permission['Value'] : true;
    },
    pagePermissionAccounting() {
      const permission = this.pagePermissions.find(a => a["Permission"] == 'Accounting'); //Accounting
      return permission ? permission['Value'] : true;
    },
    //PermissionsEnd
    overallFetch() {
      return this.pricingProps.loaded && this.pricingProps.componentFetchDone;
    },
    showLogicForShowEmailButton() {
      return this.$refs.pricingDashboard.selectedRowsJson.length > 0;
    },
    currentTimeMinusADay: {
      get() {
        const today = dateFns.startOfDay(new Date());
        return dateFns.format(today, 'MM/DD/YY') + ' 12:00:00 AM';
      }
    },
    currentTime: {
      get() {
        const today = dateFns.endOfDay(new Date());
        return dateFns.format(today, 'MM/DD/YY') + ' 11:59:59 PM';
      }
    },

    mini: {
      get() {
        return this.$store.getters.getMini;
      },
      set(value) {
        this.$store.commit('setMini', value)
      }
    },
    cLogo() {
      return mycompanylogo;
    },
    cName() {
      return mycompanyname;
    },
    initials() {
      return myfname.charAt(0) + mylname.charAt(0);
    }
  },

  async mounted() {
    EventBus.$on('onCancel', this.onCancel)
    EventBus.$on('onComponentFetchDone', this.onComponentFetchDone)

    this.sDate = this.currentTimeMinusADay;
    this.eDate = this.currentTime;

    let start = moment(this.currentTimeMinusADay).toDate();
    let end = moment(this.currentTime).toDate();
    this.range.start = start
    this.range.end = end;

    let reqClients = await fetch(`${myBaseURL}/node/quickbooks/integrated-contacts`)
    this.clients = await reqClients.json()

    
    const self = this; // Keep reference to `this` for ajaxParams
    this.tabulatorConfiguration = {
      // dataTree: true, // Server-side data doesn't typically work well with dataTree out-of-the-box
      // dataTreeStartExpanded: false,
      // dataTreeSelectPropagate: true,
      rowSelection: "checkbox",
      selectableRowsPersistence: true,
      index: "swsd_id", // Use unique ID for row tracking
      // data: this.tableData, // REMOVED: Data will be fetched via AJAX
      ajaxURL: "/node/services/service-history-dashboard2", // ADDED
      ajaxConfig: "POST",
      ajaxContentType: "json",
      ajaxParams: function() { // ADDED: To include dynamic date range and timezone
          const start = dateFns.parse(self.range.start).getTime() / 1000;
          const end = dateFns.parse(self.range.end).getTime() / 1000;
          let timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          return {
              start: start,
              end: end,
              tz: timeZone
          };
      },
      ajaxRequestFunc: async (url, config, params) => {



        const response = await fetch(url, {
          method: config.method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(params)
        });

        return response.json();
      },
      ajaxResponseFunc: function(url, params, response){
        self.tableData = response.data || [];

        // Update total matching count for select all functionality
        self.totalMatchingCount = response.total || 0;

        // Store the original response in the tabulator's AJAX module for select all functionality
        if (self.$refs.pricingDashboard && self.$refs.pricingDashboard.tabulator && self.$refs.pricingDashboard.tabulator.modules.ajax) {
          self.$refs.pricingDashboard.tabulator.modules.ajax.response = response;
        }

        // Check if filters have changed by comparing current filter state
        self.$nextTick(() => {
          const currentFilterState = self.getCurrentFilterState();
          const filtersChanged = self.hasFilterStateChanged(currentFilterState);

          console.log('🔍 AJAX Response - Filters changed:', filtersChanged);

          if (filtersChanged) {
            console.log('🔄 Filter change detected - resetting selections');
            self.clearAllSelections();
            self.isFilterChange = true; // Set flag for onDataLoaded
            self.lastFilterState = currentFilterState; // Update last known state
          } else {
            // Fallback: also try to restore selections here in case dataLoaded event doesn't fire
            if (!self.isFilterChange) {
              self.restoreSelections();
            }
          }
        });

        return {
            data: response.data || [],
            last_page: response.last_page,
            last_row: response.total, // Tabulator expects last_row for total count in remote pagination
            total: response.total
        };
    },

      placeholder: "This table is empty.",
      reactiveData: false,
      maxHeight: "100%",
      height: "100%",
      movableColumns: true,
      resizableRows: true, // this option takes a boolean value (default = false)
      downloadRowRange: "selected", //change default selector to selected
      movableRows: false,
      persistence: {
        sort: true,
        filter: true,
        columns: true
      },
      initialSort: [{
        column: "UnixTime",
        dir: "desc"
      }],
      persistenceID: "serviceHistoryTabulator1",
      layout: "fitColumns",
      pagination: "remote", 
      paginationMode: "remote", 
      filterMode: "remote", 
      sortMode: "remote", 
      paginationSize: 100,
      paginationSizeSelector: [100, 250, 500, 1000],
      paginationCounter: "rows",
      printStyled: true,
      printRowRange: "selected", //change default selector to selected
      headerFilterLiveFilterDelay: 0,
      locale: true,
      langs: {
        en: {
          pagination: {
            page_size: "Rows", //label for the page size select element
            first: "<<", //text for the first page button
            first_title: "First Page", //tooltip text for the first page button
            last: ">>",
            last_title: "Last Page",
            prev: "<",
            prev_title: "Prev Page",
            next: ">",
            next_title: "Next Page",
            counter: {
              showing: "",
              of: "of",
              rows: "rows",
              pages: "pages"
            }
          }
        }
      },
      columns: [
        {
          title: "",
          field: "",
          formatter: "rowSelection",
          titleFormatterParams: {
            rowRange: "active"
          },
          titleFormatter: "rowSelection",
          width: 50,
          hozAlign: "center",
          headerSort: false,
          frozen: true,
          print: false,
          download: false,
        },
        // {
        //   responsive:0,
        //   title: "Checkbox",
        //   cssClass: "custom-checkbox-cell",
        //   formatter: "rowSelection",
        //   titleFormatter: "rowSelection",
        //   width: 30,
        //   resizable: false,
        //   hozAlign: "center",
        //   headerSort: false,
        //   frozen: true,
        //   print: false,
        //   download: false,
        //   cellClick: function (e, cell) {
        //     cell.getRow().toggleSelect();
        //   }
        // },
        //     {
        //       title: "Expand",
        //       field: "Expand",
        //       width: 100,
        //       formatter: function(cell, formatterParams, onRendered) {
        //         return "<i class='fas fa-plus'></i>";
        //       },
        //       cellClick: function (e, cell) {
        //         var row = cell.getRow();
        //         var rowElement = row.getElement();
        //         var rowData = row.getData();
        //         rowData.condition = !rowData.condition;
        //
        //         // Check if the expandable row element already exists
        //         var expandRowElement = rowElement.getElementsByClassName("expand-row")[0];
        //         var iconElement = cell.getElement().querySelector("i");
        //
        //         if (!expandRowElement) {
        //           // Create an expandable row element
        //           expandRowElement = document.createElement("div");
        //           expandRowElement.classList.add("expand-row");
        //           expandRowElement.innerHTML = `
        //   <p>Additional details:</p>
        //   <p>Address: ${rowData.address}</p>
        //   <p>Email: ${rowData.email}</p>
        //   <p>Phone: ${rowData.phone}</p>
        // `;
        //
        //           // Append the expandable row to the main row
        //           rowElement.appendChild(expandRowElement);
        //         }
        //
        //         // Toggle the expandable row on click
        //         if (expandRowElement.style.display === "none") {
        //           expandRowElement.style.display = "block";
        //           rowData.condition = true;
        //           iconElement.classList.remove("fas", "fa-plus"); // Remove the plus icon classes
        //           iconElement.classList.add("fas", "fa-minus"); // Add the minus icon classes
        //         } else {
        //           expandRowElement.style.display = "none";
        //           rowData.condition = false;
        //           iconElement.classList.remove("fas", "fa-minus"); // Remove the minus icon classes
        //           iconElement.classList.add("fas", "fa-plus"); // Add the plus icon classes
        //         }
        //       }
        //
        //     },
        {
          title: "Service",
          field: "Service",
          width: 150,
          columnMenu: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "list",
          headerFilterParams: () => { 
            return {
              clearable: true,
              multiselect: true,
              valuesLookup: () => self.filterValues['Service'] || []
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          headerHozAlign: "center",
          frozen: true,
          resizable: true,
          formatter: function (cell, formatterParams) {
            const value = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.VendorId == myvtem) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
            else {
              return (
                "<span style='color:#000; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          }
        },
        {
          title: "Status",
          field: "Status",
          accessorDownload: function (value, data, type, params, column) {
            if (value == "INVOICED_BILLED") {
              value = "INVOICED / BILLED"
            }
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: () => {
            return { 
              valuesLookup: () => self.filterValues['Status'] || [],
              clearable: true,
              multiselect: true
            }
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (headerValue.includes(rowValue)) {
              return true;
            }
          },
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            value = value == null ? '' : value;
            if (value == "Logged - Mobile") {
              cell.getElement().style.backgroundColor = "#1976d2";
              cell.getElement().style.color = "white";
            } else if (value == "PENDING") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            } else if (value == "Logged - Admin") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            } else if (value == "SENT") {
              cell.getElement().style.backgroundColor = "#a707ea";
              cell.getElement().style.color = "white";
            } else if (value == "PostDated - Admin") {
              cell.getElement().style.backgroundColor = "#ff5252";
              cell.getElement().style.color = "white";
            } else if (value.toLowerCase() == "logged") {
              cell.getElement().style.backgroundColor = "#1976d2";
              cell.getElement().style.color = "white";
            } else if (value == "APPROVED") {
              cell.getElement().style.backgroundColor = "#4BB543";
              cell.getElement().style.color = "white";
            }
            else if (value == "LOCKED") {
              cell.getElement().style.backgroundColor = "#2a6525";
              cell.getElement().style.color = "white";
            }
            else if (value == "REJECTED") {
              cell.getElement().style.backgroundColor = "#e79c38";
              cell.getElement().style.color = "white";
            }
            else if (value == "INVOICED") {
              cell.getElement().style.backgroundColor = "#caad25";
              cell.getElement().style.color = "white";
            }
            else if (value == "BILLED") {
              cell.getElement().style.backgroundColor = "#6f3f82";
              cell.getElement().style.color = "white";
            }
            else if (value == "INVOICED_BILLED") {
              cell.getElement().style.backgroundColor = "#27ae60";
              cell.getElement().style.color = "white";
              value = "INVOICED / BILLED";
            }
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";
            return value;
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Source",
          field: "Source",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['Source'] || [],
            clearable: true
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Trade",
          field: "TradeTitle",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          resizable: false,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['TradeTitle'] || [],
            clearable: true
          },
          width: 150,
          headerHozAlign: "center"
        },
        {
          title: "Site",
          field: "Site",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerFilterLiveFilter: false,
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let siteId = rowData.site_id;
            if (siteId != null) {
              self.navigateToRouteNewTab('sitesid', siteId);
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value == null) {
              value = "";
            }
            return (
              "<span style='color:#1867c0; font-weight:bold;'>" +
              value +
              "</span>"
            );
          }
        },
        {
          title: "Site ID",
          field: "site_id",
          width: 150,
          visible: false,
          download: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "Address",
          field: "Address",
          width: 200,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "City",
          field: "City",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "State",
          field: "State",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Zip",
          field: "Zip",
          width: 170,
          visible: false,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
        },
        {
          title: "Zone",
          field: "Zone",
          width: 170,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list", 
          headerFilterParams: {
            valuesLookup: () => self.filterValues['Zone'] || [],
            clearable: true
          },
          headerHozAlign: "center",
        },
        {
          title: "Service Details",
          field: "ServiceDetails",
          width: 150,
          headerFilter: "input"
        },
        {
          title: "Internal Manager",
          field: "InternalManager",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerHozAlign: "center",
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['InternalManager'] || [],
            clearable: true
            },
        },
        {
          title: "Notes",
          field: "Notes",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            self.addUpdateServiceNotes(rowData, newValue)
          }
        },
        {
          title: "Internal WO#",
          field: "WorkOrder",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let value = cell.getValue();
            if (value != null) {
              let workOrderId = rowData.WorkOrder;
              window.open(myBaseURL + `/home#/workorder-view/${workOrderId}`, '_blank');
            }
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value != null) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          }
        },
        {
          title: "Work Order",
          field: "ExternalWorkOrder",
          sorter: "alphanum",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            if (value != null) {
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let value = cell.getValue();
            if (value != null) {
              if (rowData.WoSystemId == '2') {
                let url = self.getServiceChannelURL(rowData.ExternalWorkOrder)
                window.open(url, '_blank');
              }
            }
          }
        },
        {
          title: "Form ID",
          field: "ParentFormId",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            self.navigateToRouteNewTab('formbuilderid', cell.getValue());
          },
        },
        {
          title: "Form",
          field: "Form",
          width: 150,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "input",
          headerHozAlign: "center",
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            let table = row.getTable();
            let formId = rowData.FormId;
            self.openForm(formId);
          },
          formatter: function (cell, formatterParams, onRendered) {
            let row = cell.getRow();
            let rowData = row.getData();
            let value = cell.getValue();
            let formId = rowData.FormId;

            value = value || '';

            cell.getElement().style.color = "white";
            cell.getElement().style.fontWeight = "bold";
            cell.getElement().style.textTransform = "capitalize";

            let prevRow = row.getPrevRow();
            let prevRowData = prevRow ? prevRow.getData() : null;

            if (!prevRowData || prevRowData.FormId !== formId) {
              currentColor = (currentColor === colors[0]) ? colors[1] : colors[0];
            }

            cell.getElement().style.backgroundColor = currentColor;
            return `${value} - ${formId}`;
          },
        },
        {
          title: "Client Unit",
          field: "ClientPricingUnit",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list",
          headerFilterParams: {
              valuesLookup: () => self.filterValues['ClientPricingUnit'] || [],
            clearable: true,
            multiselect: true
            },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (Array.isArray(headerValue) && headerValue.includes(rowValue)) { 
              return true;
            }
             return false; 
          },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerHozAlign: "center",
          formatter: function (cell, formatterParams) {
            let row = cell.getRow();
            let rowData = row.getData();
            let value = cell.getValue() === null ? '' : cell.getValue();
            if (rowData.VendorId == myvtem){
              return (
                "<span style='color:#1867c0; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
            else {
              return (
                "<span style='color:#000; font-weight:bold;'>" +
                value +
                "</span>"
              );
            }
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.ClientPricingUnit != null && rowData.ParentId !== null && rowData.VendorId == myvtem){
              self.openPricingContract(undefined, row);
            }
          },
        },
        {
          title: "Contractor Unit",
          field: "ContractorPricingUnit",
          width: 200,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          resizable: true,
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['ContractorPricingUnit'] || [],
            clearable: true,
            multiselect: true
            },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerHozAlign: "center",
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (Array.isArray(headerValue) && headerValue.includes(rowValue)) { 
              return true;
            }
            return false; 
          },
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            return (
              "<span style='color:#1867c0; font-weight:bold;'>" +
              value +
              "</span>"
            );
          },
          cellClick: function (e, cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.ParentIdContractor !== null) {
              self.openPricingContractForContractor(undefined, row);
            }
          },
        },
        {
          title: "People",
          field: "People",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: (cell) => {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.People = newValue;
              self.applyPeopleToBulk(true, [rowData], newValue);
            }
          },
        },
        {
          title: "Hours",
          field: "Hours",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.Hours = newValue;
              self.applyHoursToBulk(true, [rowData], newValue);
            }
          },
        },
        {
          title: "Out-In",
          field: "OutIn",
          hozAlign: "center",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            let value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          }
        },
        {
          title: "CheckIn",
          field: "Start",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "daterange",
          resizable: false,
          width: 150,
          formatter: function(cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: function(value, data, type, params, column){

            if (value != null && value !== "") {
              try {

                let dt = luxon.DateTime.fromSeconds(value);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
        },
        {
          title: "CheckOut",
          field: "Stop",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "number",
          headerFilter: "daterange",
          resizable: false,
          width: 150,
          formatter: function(cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: function(value, data, type, params, column){

            if (value != null && value !== "") {
              try {

                let dt = luxon.DateTime.fromSeconds(value);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
        },
        {
          title: "Snow API",
          field: "SnowAccuWeather",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 150,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          }
        },
        {
          title: "Snow*",
          field: "snow_inch",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 110,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          editor: "input",
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (self.pagePermissionAdmin && rowData.VendorId == myvtem) {
              return true;
            }
            return false;
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            if ( parseInt(newValue) < 0 ){
              cell.setValue(0, true);
            }
            else {
              rowData.snow_inch = newValue;
              self.applySnowTotalBulk(true, [rowData], newValue);
            }
          },
          editorParams: {
            elementAttributes: {
              type: "number"
            }
          }
        },
        {
          title: "Event",
          field: "swsd_event",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          headerFilter: "list",
          headerFilterParams: {
            valuesLookup: () => self.filterValues['swsd_event'] || [],
            clearable: true,
            multiselect: true
            },
          headerFilterEmptyCheck: (value) => {
            return value.length == 0;
          },
          headerFilterFunc: (headerValue, rowValue, rowData, filterParams) => {
            if (Array.isArray(headerValue) && headerValue.includes(rowValue)) { // Ensure headerValue is array
              return true;
            }
            return false; // Default if not array or not included
          },
          resizable: false,
          width: 160,
          formatter: function (cell, formatterParams, onRendered) {
            var value = cell.getValue();
            cell.getElement().style.fontWeight = 500;
            return value;
          },
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          editor: "list",
          editorParams: {
            valuesLookup: "active"
          },
          editable: function (cell) {
            let row = cell.getRow();
            let rowData = row.getData();
            if (rowData.VendorId == myvtem) {
              return true;
            }
            else {
              return false;
            }
          },
          cellEdited: function (cell) {
            let newValue = cell.getValue();
            let row = cell.getRow();
            let rowData = row.getData();
            self.eventName = newValue;
            self.applyEventBulk(true, [rowData], newValue)
          },
        },
        {
          title: "Client Pricing",
          field: "ClientPricingUi",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            value = `$${parseFloat(value).toFixed(2)}`
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Contractor Pricing",
          field: "ContractorPricingUi",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let value = cell.getValue();
            value = `$${parseFloat(value).toFixed(2)}`
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Provider",
          field: "ProviderText",
          sorter: "string",
          resizable: true,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "Provider ID",
          field: "contractors",
          width: 150,
          visible: false,
          download: true,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "Client",
          field: "Client",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "QB Invoice ID",
          field: "swsd_qb_invoice_id",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "QB Bill ID",
          field: "swsd_qb_bill_id",
          sorter: "string",
          resizable: true,
          //width: 150,
          minWidth: 120,
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          headerHozAlign: "center"
        },
        {
          title: "Material Unit",
          field: "MaterialQuantity",
          hozAlign: "right",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 200,
          formatter: function (cell, formatterParams) {
            let rowData = cell.getRow().getData();
            let value = cell.getValue();
            if ( !isNaN(parseFloat(value)) && parseFloat(value) > 0){
              value = `${value} ${rowData.MaterialUnit}`;
            }else {
              value = "";
            }
            return (
              "<span>" +
              value +
              "</span>"
            );
          },
        },
        {
          title: "Metadata",
          field: "metadata",
          resizable: true,
          width: 200,
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
        },
        {
          title: "ID",
          field: "swsd_id",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 100,
        },
        {
          title: "Form Submission ID",
          field: "form_submission_id",
          hozAlign: "center",
          headerHozAlign: "center",
          sorter: "string",
          accessorDownload: function (value, data, type, params, column) {
            return (value == null) ? "" : value;
          },
          headerFilter: "input",
          resizable: false,
          width: 100,
        },

        {
          title: "Date",
          field: "UnixTime",
          headerHozAlign: "center",
          sorter: "number",
          hozAlign: "center",
          headerFilter: "daterange",
          resizable: true,
          frozen: true,
          formatter: function (cell, formatterParams, onRendered) {
            try {
              let dt = luxon.DateTime.fromSeconds(cell.getValue());
              return dt.toFormat(formatterParams.outputFormat);
            } catch (error) {
              return formatterParams.invalidPlaceholder;
            }
          },
          formatterParams: {
            outputFormat: "MM/dd/yy hh:mm a",
            invalidPlaceholder: ""
          },
          accessorDownload: function (value, data, type, params, column) {

            if (value != null && value !== "") {
              try {

                let dt = luxon.DateTime.fromSeconds(value);

                return dt.toFormat(column.getDefinition().formatterParams.outputFormat);
              } catch (error) {

                return column.getDefinition().formatterParams.invalidPlaceholder;
              }
            } else {

              return "";
            }
          },
          minWidth: 180,

        }

      ]
    }
    this.fetchData();
  },

  asyncComputed: {
    exportFileName: {
      get() {
        return 'PostDatedServices-' + moment().format("MM-DD-YY")
      }
    },
  }
}
