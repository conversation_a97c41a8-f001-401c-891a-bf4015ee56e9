const express = require('express');
const controller = require('../controllers/services-trades-equipment.controller');
const router = express.Router();

router.get('/trades', controller.getTrades);
router.get('/equipment', controller.getEquipment);
router.get('/service-types', controller.getServiceTypes);
router.get('/service-history-dashboard', controller.getServiceHistoryDashboard);
router.get('/worker-history-dashboard', controller.getWorkerHistoryDashboard);
router.post('/update-submitted-service', controller.updateSubmittedService);
router.post('/service-status', controller.changeServiceStatus);
router.post('/service-notes', controller.addUpdateServiceNotes);
router.post('/send-services-postdated', controller.sendServicesPostdated);
router.post('/update-postdated', controller.updateReportEmail);
router.post('/add-services-postdated', controller.addNewServicesToReportEmail);
router.post('/delete-service', controller.deleteService);
router.get('/email-data', controller.loadEmailData);
router.post('/remove-services-from-report', controller.removeServicesFromPostdatedEmailWronglySent);
router.post('/update-adjusted-checkinsouts', controller.updatedAdjustedCheckInOutForWorkerPayroll);
router.post('/update-worker-payroll-lineitems', controller.updateWorkerPayrollLineItems);
router.post('/log-time-management', controller.clockInClockOut);
router.get('/boservicesget', controller.boservicesget);
router.post('/service-history-dashboard2', controller.getServiceHistoryDashboard2);
router.get('/service-history-filter-values', controller.getServiceHistoryFilterValues);
router.post('/bulk-action-all-matching', controller.bulkActionAllMatching);
router.post('/get-services-by-ids', controller.getServicesByIds);
module.exports = router
